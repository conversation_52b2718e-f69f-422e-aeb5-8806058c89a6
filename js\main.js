// 路由配置
const routes = [
    {
        path: '/',
        component: {
            template: '#dashboard-template',
            data() {
                return {
                    loading: true,
                    statistics: {
                        totalTrains: 0,
                        runningTrains: 0,
                        warningCount: 0,
                        safetyScore: 0
                    },
                    recentWarnings: [],
                    trainStatusData: []
                };
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.statistics = {
                        totalTrains: 128,
                        runningTrains: 87,
                        warningCount: 3,
                        safetyScore: 96.8
                    };
                    this.recentWarnings = [
                        { id: 'W2023112201', trainId: 'T1208', type: '温度异常', level: '中', time: '2023-11-22 08:32:15', status: '已处理' },
                        { id: 'W2023112105', trainId: 'T0537', type: '制动系统异常', level: '高', time: '2023-11-21 15:47:23', status: '处理中' },
                        { id: 'W2023112003', trainId: 'T0912', type: '信号干扰', level: '低', time: '2023-11-20 10:15:42', status: '已处理' }
                    ];
                    this.trainStatusData = [
                        { trainId: 'T1208', line: '1号线', status: '正常运行', speed: '60km/h', position: '西单站-复兴门站', nextStation: '复兴门站', arrivalTime: '2分钟' },
                        { trainId: 'T0537', line: '2号线', status: '减速运行', speed: '45km/h', position: '朝阳门站-东四十条站', nextStation: '东四十条站', arrivalTime: '4分钟' },
                        { trainId: 'T0912', line: '4号线', status: '正常运行', speed: '58km/h', position: '北京南站-陶然亭站', nextStation: '陶然亭站', arrivalTime: '3分钟' }
                    ];
                    this.initCharts();
                }, 1000);
            },
            methods: {
                initCharts() {
                    // 初始化列车运行状态统计图表
                    const statusChart = echarts.init(document.getElementById('train-status-chart'));
                    statusChart.setOption({
                        title: {
                            text: '列车运行状态分布',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c} ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            left: 'left',
                            data: ['正常运行', '减速运行', '临时停车', '检修中', '未运行']
                        },
                        series: [
                            {
                                name: '运行状态',
                                type: 'pie',
                                radius: '60%',
                                center: ['50%', '50%'],
                                data: [
                                    { value: 78, name: '正常运行' },
                                    { value: 9, name: '减速运行' },
                                    { value: 3, name: '临时停车' },
                                    { value: 12, name: '检修中' },
                                    { value: 26, name: '未运行' }
                                ],
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    });

                    // 初始化故障类型统计图表
                    const warningChart = echarts.init(document.getElementById('warning-chart'));
                    warningChart.setOption({
                        title: {
                            text: '近30天故障类型统计',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        legend: {
                            data: ['低级故障', '中级故障', '高级故障'],
                            top: 'bottom'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: ['信号系统', '动力系统', '制动系统', '车门系统', '空调系统', '监控系统', '其他']
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [
                            {
                                name: '低级故障',
                                type: 'bar',
                                stack: 'total',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: [12, 8, 5, 7, 9, 3, 4]
                            },
                            {
                                name: '中级故障',
                                type: 'bar',
                                stack: 'total',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: [5, 4, 3, 2, 3, 1, 2]
                            },
                            {
                                name: '高级故障',
                                type: 'bar',
                                stack: 'total',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: [1, 2, 3, 0, 1, 0, 1]
                            }
                        ]
                    });

                    // 初始化运行效率统计图表
                    const efficiencyChart = echarts.init(document.getElementById('efficiency-chart'));
                    efficiencyChart.setOption({
                        title: {
                            text: '近7天运行效率统计',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        legend: {
                            data: ['准点率', '满载率', '运行效率'],
                            top: 'bottom'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            boundaryGap: false,
                            data: ['11-16', '11-17', '11-18', '11-19', '11-20', '11-21', '11-22']
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                formatter: '{value}%'
                            }
                        },
                        series: [
                            {
                                name: '准点率',
                                type: 'line',
                                data: [98.2, 97.8, 98.5, 99.1, 98.7, 97.9, 98.4]
                            },
                            {
                                name: '满载率',
                                type: 'line',
                                data: [85.3, 87.2, 86.5, 88.7, 90.1, 89.5, 88.2]
                            },
                            {
                                name: '运行效率',
                                type: 'line',
                                data: [92.5, 91.8, 93.2, 94.5, 93.8, 92.7, 93.5]
                            }
                        ]
                    });

                    // 监听窗口大小变化，重新调整图表大小
                    window.addEventListener('resize', function() {
                        statusChart.resize();
                        warningChart.resize();
                        efficiencyChart.resize();
                    });
                },
                getStatusClass(status) {
                    if (status === '正常运行') return 'status-normal';
                    if (status === '减速运行') return 'status-warning';
                    if (status === '临时停车') return 'status-danger';
                    return '';
                },
                getWarningLevelClass(level) {
                    if (level === '高') return 'warning-level-high';
                    if (level === '中') return 'warning-level-medium';
                    if (level === '低') return 'warning-level-low';
                    return '';
                }
            }
        }
    },
    {
        path: '/train-status',
        component: {
            template: '#train-status-template',
            data() {
                return {
                    loading: true,
                    trainList: [],
                    searchQuery: '',
                    filterLine: '',
                    filterStatus: '',
                    currentPage: 1,
                    pageSize: 10,
                    autoRefresh: true,
                    refreshInterval: null,
                    statusStats: [],
                    realtimeData: [],
                    selectedTrain: null,
                    detailDialogVisible: false,
                    detailActiveTab: 'basic'
                };
            },
            computed: {
                filteredTrains() {
                    let filtered = this.trainList.filter(train => {
                        const matchQuery = train.trainId.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                                          train.line.toLowerCase().includes(this.searchQuery.toLowerCase());
                        const matchLine = this.filterLine ? train.line === this.filterLine : true;
                        const matchStatus = this.filterStatus ? train.status === this.filterStatus : true;
                        return matchQuery && matchLine && matchStatus;
                    });

                    // 分页处理
                    const start = (this.currentPage - 1) * this.pageSize;
                    const end = start + this.pageSize;
                    return filtered.slice(start, end);
                }
            },
            mounted() {
                this.loadTrainData();
                this.initMap();
                this.startAutoRefresh();
            },
            beforeDestroy() {
                this.stopAutoRefresh();
            },
            methods: {
                loadTrainData() {
                    this.loading = true;
                    // 模拟数据加载
                    setTimeout(() => {
                        this.loading = false;
                        this.initTrainList();
                        this.initStatusStats();
                        this.initRealtimeData();
                    }, 1000);
                },
                initTrainList() {
                    this.trainList = [
                        {
                            trainId: 'T1208',
                            line: '1号线',
                            status: '正常运行',
                            speed: '60km/h',
                            position: '西单站-复兴门站',
                            nextStation: '复兴门站',
                            arrivalTime: '2分钟',
                            temperature: '22°C',
                            humidity: '45%',
                            passengers: '较多',
                            lastUpdate: '2023-11-22 14:30:25',
                            maxSpeed: '80km/h',
                            carriageCount: '6节',
                            capacity: '1200人',
                            currentPassengers: '850人',
                            loadRate: '70.8%',
                            equipmentStatus: [
                                { system: '动力系统', status: '正常', value: '98%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '运行良好' },
                                { system: '制动系统', status: '正常', value: '96%', normal: '90-100%', lastCheck: '2023-11-22 08:00', description: '制动性能正常' },
                                { system: '信号系统', status: '正常', value: '99%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '信号接收良好' },
                                { system: '空调系统', status: '警告', value: '85%', normal: '90-100%', lastCheck: '2023-11-22 08:00', description: '制冷效果略差' },
                                { system: '车门系统', status: '正常', value: '97%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '开关正常' }
                            ],
                            maintenanceHistory: [
                                { date: '2023-11-15', type: '定期保养', items: '更换制动片、检查信号设备', technician: '张师傅', duration: '4小时', cost: '¥2,500', result: '合格' },
                                { date: '2023-10-20', type: '故障维修', items: '修复空调压缩机', technician: '李师傅', duration: '6小时', cost: '¥4,200', result: '合格' },
                                { date: '2023-10-01', type: '定期保养', items: '全面检查、清洁保养', technician: '王师傅', duration: '8小时', cost: '¥3,800', result: '合格' }
                            ]
                        },
                        {
                            trainId: 'T0537',
                            line: '2号线',
                            status: '减速运行',
                            speed: '45km/h',
                            position: '朝阳门站-东四十条站',
                            nextStation: '东四十条站',
                            arrivalTime: '4分钟',
                            temperature: '24°C',
                            humidity: '48%',
                            passengers: '中等',
                            lastUpdate: '2023-11-22 14:30:18',
                            maxSpeed: '80km/h',
                            carriageCount: '6节',
                            capacity: '1200人',
                            currentPassengers: '620人',
                            loadRate: '51.7%',
                            equipmentStatus: [
                                { system: '动力系统', status: '警告', value: '88%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '功率输出偏低' },
                                { system: '制动系统', status: '正常', value: '94%', normal: '90-100%', lastCheck: '2023-11-22 08:00', description: '制动正常' },
                                { system: '信号系统', status: '正常', value: '97%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '信号良好' },
                                { system: '空调系统', status: '正常', value: '92%', normal: '90-100%', lastCheck: '2023-11-22 08:00', description: '运行正常' },
                                { system: '车门系统', status: '正常', value: '95%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '开关正常' }
                            ],
                            maintenanceHistory: [
                                { date: '2023-11-18', type: '故障检查', items: '检查动力系统异常', technician: '赵师傅', duration: '2小时', cost: '¥800', result: '待处理' },
                                { date: '2023-11-10', type: '定期保养', items: '常规检查保养', technician: '陈师傅', duration: '4小时', cost: '¥2,200', result: '合格' }
                            ]
                        },
                        {
                            trainId: 'T0912',
                            line: '4号线',
                            status: '正常运行',
                            speed: '58km/h',
                            position: '北京南站-陶然亭站',
                            nextStation: '陶然亭站',
                            arrivalTime: '3分钟',
                            temperature: '21°C',
                            humidity: '42%',
                            passengers: '较少',
                            lastUpdate: '2023-11-22 14:30:32',
                            maxSpeed: '80km/h',
                            carriageCount: '6节',
                            capacity: '1200人',
                            currentPassengers: '380人',
                            loadRate: '31.7%',
                            equipmentStatus: [
                                { system: '动力系统', status: '正常', value: '99%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '运行优秀' },
                                { system: '制动系统', status: '正常', value: '98%', normal: '90-100%', lastCheck: '2023-11-22 08:00', description: '制动优秀' },
                                { system: '信号系统', status: '正常', value: '100%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '信号完美' },
                                { system: '空调系统', status: '正常', value: '94%', normal: '90-100%', lastCheck: '2023-11-22 08:00', description: '运行良好' },
                                { system: '车门系统', status: '正常', value: '99%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '开关顺畅' }
                            ],
                            maintenanceHistory: [
                                { date: '2023-11-12', type: '定期保养', items: '全面检查、润滑保养', technician: '孙师傅', duration: '5小时', cost: '¥2,800', result: '合格' },
                                { date: '2023-10-25', type: '定期保养', items: '常规检查', technician: '周师傅', duration: '3小时', cost: '¥1,800', result: '合格' }
                            ]
                        },
                        {
                            trainId: 'T0315',
                            line: '5号线',
                            status: '正常运行',
                            speed: '62km/h',
                            position: '惠新西街南口站-和平西桥站',
                            nextStation: '和平西桥站',
                            arrivalTime: '2分钟',
                            temperature: '23°C',
                            humidity: '44%',
                            passengers: '中等',
                            lastUpdate: '2023-11-22 14:30:28',
                            maxSpeed: '80km/h',
                            carriageCount: '6节',
                            capacity: '1200人',
                            currentPassengers: '720人',
                            loadRate: '60.0%',
                            equipmentStatus: [
                                { system: '动力系统', status: '正常', value: '96%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '运行良好' },
                                { system: '制动系统', status: '正常', value: '97%', normal: '90-100%', lastCheck: '2023-11-22 08:00', description: '制动良好' },
                                { system: '信号系统', status: '正常', value: '98%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '信号稳定' },
                                { system: '空调系统', status: '正常', value: '93%', normal: '90-100%', lastCheck: '2023-11-22 08:00', description: '运行正常' },
                                { system: '车门系统', status: '正常', value: '96%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '开关正常' }
                            ],
                            maintenanceHistory: [
                                { date: '2023-11-08', type: '定期保养', items: '检查更换易损件', technician: '吴师傅', duration: '4小时', cost: '¥2,600', result: '合格' }
                            ]
                        },
                        {
                            trainId: 'T0721',
                            line: '6号线',
                            status: '临时停车',
                            speed: '0km/h',
                            position: '草房站',
                            nextStation: '常营站',
                            arrivalTime: '未知',
                            temperature: '25°C',
                            humidity: '50%',
                            passengers: '较多',
                            lastUpdate: '2023-11-22 14:25:15',
                            maxSpeed: '80km/h',
                            carriageCount: '6节',
                            capacity: '1200人',
                            currentPassengers: '950人',
                            loadRate: '79.2%',
                            equipmentStatus: [
                                { system: '动力系统', status: '故障', value: '65%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '功率异常，需检修' },
                                { system: '制动系统', status: '正常', value: '95%', normal: '90-100%', lastCheck: '2023-11-22 08:00', description: '制动正常' },
                                { system: '信号系统', status: '正常', value: '96%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '信号正常' },
                                { system: '空调系统', status: '正常', value: '91%', normal: '90-100%', lastCheck: '2023-11-22 08:00', description: '运行正常' },
                                { system: '车门系统', status: '正常', value: '94%', normal: '95-100%', lastCheck: '2023-11-22 08:00', description: '开关正常' }
                            ],
                            maintenanceHistory: [
                                { date: '2023-11-22', type: '紧急维修', items: '检修动力系统故障', technician: '维修中', duration: '进行中', cost: '待定', result: '处理中' },
                                { date: '2023-11-05', type: '定期保养', items: '常规检查保养', technician: '郑师傅', duration: '4小时', cost: '¥2,400', result: '合格' }
                            ]
                        }
                    ];
                },
                initStatusStats() {
                    // 初始化状态统计数据
                    this.statusStats = [
                        {
                            type: 'total',
                            icon: 'el-icon-s-grid',
                            label: '运行列车',
                            value: this.trainList.length,
                            change: '+2',
                            trend: 'positive'
                        },
                        {
                            type: 'normal',
                            icon: 'el-icon-success',
                            label: '正常运行',
                            value: this.trainList.filter(t => t.status === '正常运行').length,
                            change: '+1',
                            trend: 'positive'
                        },
                        {
                            type: 'warning',
                            icon: 'el-icon-warning',
                            label: '异常状态',
                            value: this.trainList.filter(t => t.status !== '正常运行').length,
                            change: '+1',
                            trend: 'negative'
                        },
                        {
                            type: 'speed',
                            icon: 'el-icon-timer',
                            label: '平均速度',
                            value: this.calculateAverageSpeed() + 'km/h',
                            change: '+2.5km/h',
                            trend: 'positive'
                        }
                    ];
                },
                initRealtimeData() {
                    // 初始化实时监控数据
                    this.realtimeData = [
                        { label: '系统状态', value: '正常', status: 'normal', trend: 'stable', change: '稳定' },
                        { label: '信号强度', value: '98%', status: 'good', trend: 'positive', change: '+2%' },
                        { label: '网络延迟', value: '15ms', status: 'good', trend: 'negative', change: '-3ms' },
                        { label: '数据更新', value: '实时', status: 'normal', trend: 'stable', change: '正常' },
                        { label: '告警数量', value: '2个', status: 'warning', trend: 'positive', change: '+1' },
                        { label: '在线率', value: '100%', status: 'good', trend: 'stable', change: '稳定' }
                    ];
                },
                calculateAverageSpeed() {
                    const speeds = this.trainList.map(train => parseFloat(train.speed.replace('km/h', '')));
                    const total = speeds.reduce((sum, speed) => sum + speed, 0);
                    return Math.round(total / speeds.length);
                },
                refreshTrainData() {
                    this.loadTrainData();
                    this.$message({
                        message: '列车数据已刷新',
                        type: 'success'
                    });
                },
                startAutoRefresh() {
                    if (this.autoRefresh) {
                        this.refreshInterval = setInterval(() => {
                            this.loadTrainData();
                        }, 30000); // 每30秒刷新一次
                    }
                },
                stopAutoRefresh() {
                    if (this.refreshInterval) {
                        clearInterval(this.refreshInterval);
                        this.refreshInterval = null;
                    }
                },
                initMap() {
                    // 初始化地图，这里可以集成百度地图、高德地图等
                    if (document.getElementById('train-map')) {
                        // 模拟地图初始化
                        const mapContainer = document.getElementById('train-map');
                        mapContainer.innerHTML = `
                            <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f5f7fa; border-radius: 4px;">
                                <div style="text-align: center; color: #909399;">
                                    <i class="el-icon-map-location" style="font-size: 48px; margin-bottom: 10px; display: block;"></i>
                                    <p>列车运行地图</p>
                                    <p style="font-size: 12px;">实际项目中可集成百度地图、高德地图等</p>
                                    <div style="margin-top: 20px;">
                                        ${this.trainList.map(train => `
                                            <div style="margin: 5px 0; padding: 5px 10px; background: #fff; border-radius: 4px; font-size: 12px;">
                                                ${train.trainId} - ${train.line} - ${train.position}
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                },
                openMap() {
                    // 打开全屏地图
                    this.$alert('全屏地图功能开发中...', '提示', {
                        confirmButtonText: '确定'
                    });
                },
                getTrendIcon(trend) {
                    const iconMap = {
                        'positive': 'el-icon-top',
                        'negative': 'el-icon-bottom',
                        'stable': 'el-icon-minus'
                    };
                    return iconMap[trend] || 'el-icon-minus';
                },
                getStatusType(status) {
                    const typeMap = {
                        '正常运行': 'success',
                        '减速运行': 'warning',
                        '临时停车': 'danger'
                    };
                    return typeMap[status] || 'info';
                },
                getSpeedColor(speed) {
                    const speedValue = parseFloat(speed.replace('km/h', ''));
                    if (speedValue >= 50) return '#67C23A';
                    if (speedValue >= 30) return '#E6A23C';
                    return '#F56C6C';
                },
                getTemperatureColor(temperature) {
                    const tempValue = parseFloat(temperature.replace('°C', ''));
                    if (tempValue >= 18 && tempValue <= 26) return '#67C23A';
                    if (tempValue >= 15 && tempValue <= 30) return '#E6A23C';
                    return '#F56C6C';
                },
                getPassengerType(passengers) {
                    const typeMap = {
                        '较少': 'success',
                        '中等': 'warning',
                        '较多': 'danger'
                    };
                    return typeMap[passengers] || 'info';
                },
                selectTrain(train) {
                    this.selectedTrain = train;
                },
                viewTrainDetail(train) {
                    this.selectedTrain = train;
                    this.detailDialogVisible = true;
                    this.detailActiveTab = 'basic';

                    // 初始化性能图表
                    this.$nextTick(() => {
                        this.initPerformanceChart();
                    });
                },
                viewTrainTrack(train) {
                    this.$router.push({
                        path: '/train-track',
                        query: { trainId: train.trainId }
                    });
                },
                controlTrain(train) {
                    this.$confirm(`确定要对列车 ${train.trainId} 进行控制操作吗？`, '列车控制', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.$message({
                            type: 'success',
                            message: '控制指令已发送'
                        });
                    }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消操作'
                        });
                    });
                },
                viewTrainHistory(train) {
                    this.$alert(`查看列车 ${train.trainId} 的历史记录功能开发中...`, '历史记录', {
                        confirmButtonText: '确定'
                    });
                },
                exportTrainData() {
                    // 导出列车数据
                    const data = this.trainList.map(train => ({
                        '列车号': train.trainId,
                        '线路': train.line,
                        '状态': train.status,
                        '速度': train.speed,
                        '位置': train.position,
                        '下一站': train.nextStation,
                        '预计到达': train.arrivalTime,
                        '温度': train.temperature,
                        '湿度': train.humidity,
                        '乘客情况': train.passengers,
                        '更新时间': train.lastUpdate
                    }));

                    const csvContent = this.convertToCSV(data);
                    this.downloadCSV(csvContent, '列车运行状态数据.csv');

                    this.$message({
                        message: '数据导出成功',
                        type: 'success'
                    });
                },
                exportTrainDetail() {
                    if (!this.selectedTrain) return;

                    const data = {
                        基本信息: this.selectedTrain,
                        设备状态: this.selectedTrain.equipmentStatus,
                        维护记录: this.selectedTrain.maintenanceHistory
                    };

                    const jsonContent = JSON.stringify(data, null, 2);
                    const blob = new Blob([jsonContent], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `列车${this.selectedTrain.trainId}详细信息.json`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);

                    this.$message({
                        message: '详细信息导出成功',
                        type: 'success'
                    });
                },
                convertToCSV(data) {
                    if (!data.length) return '';

                    const headers = Object.keys(data[0]);
                    const csvRows = [headers.join(',')];

                    data.forEach(row => {
                        const values = headers.map(header => {
                            const value = row[header];
                            return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                        });
                        csvRows.push(values.join(','));
                    });

                    return '\uFEFF' + csvRows.join('\n'); // 添加BOM以支持中文
                },
                downloadCSV(content, filename) {
                    const blob = new Blob([content], { type: 'text/csv;charset=utf-8' });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                },
                initPerformanceChart() {
                    if (document.getElementById('train-performance-chart')) {
                        const chart = echarts.init(document.getElementById('train-performance-chart'));
                        chart.setOption({
                            title: {
                                text: '列车性能指标',
                                left: 'center'
                            },
                            tooltip: {
                                trigger: 'axis'
                            },
                            legend: {
                                data: ['速度', '温度', '湿度'],
                                top: 'bottom'
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: ['14:00', '14:05', '14:10', '14:15', '14:20', '14:25', '14:30']
                            },
                            yAxis: [
                                {
                                    type: 'value',
                                    name: '速度(km/h)',
                                    position: 'left'
                                },
                                {
                                    type: 'value',
                                    name: '温度/湿度',
                                    position: 'right'
                                }
                            ],
                            series: [
                                {
                                    name: '速度',
                                    type: 'line',
                                    data: [58, 62, 60, 65, 63, 45, 60],
                                    itemStyle: { color: '#409EFF' }
                                },
                                {
                                    name: '温度',
                                    type: 'line',
                                    yAxisIndex: 1,
                                    data: [21, 22, 22, 23, 23, 24, 22],
                                    itemStyle: { color: '#67C23A' }
                                },
                                {
                                    name: '湿度',
                                    type: 'line',
                                    yAxisIndex: 1,
                                    data: [42, 43, 44, 45, 46, 48, 45],
                                    itemStyle: { color: '#E6A23C' }
                                }
                            ]
                        });
                    }
                },
                handleSizeChange(val) {
                    this.pageSize = val;
                    this.currentPage = 1;
                },
                handleCurrentChange(val) {
                    this.currentPage = val;
                }
            },
            watch: {
                autoRefresh(newVal) {
                    if (newVal) {
                        this.startAutoRefresh();
                    } else {
                        this.stopAutoRefresh();
                    }
                }
            }
        }
    },
    {
        path: '/train-track',
        component: {
            template: '#train-track-template',
            data() {
                return {
                    loading: false,
                    selectedTrain: '',
                    dateValue: new Date(),
                    timeRange: null,
                    trainOptions: [],
                    trackData: [],
                    trackSearchQuery: '',
                    trackStatusFilter: '',
                    trackCurrentPage: 1,
                    trackPageSize: 20,
                    showRealTime: false,
                    isPlaying: false,
                    playbackProgress: 0,
                    playbackSpeed: 1,
                    playbackInterval: null,
                    currentTrackPoint: null,
                    trackStatistics: []
                };
            },
            computed: {
                filteredTrackData() {
                    let filtered = this.trackData;

                    // 搜索过滤
                    if (this.trackSearchQuery) {
                        filtered = filtered.filter(item =>
                            item.station.toLowerCase().includes(this.trackSearchQuery.toLowerCase()) ||
                            item.status.toLowerCase().includes(this.trackSearchQuery.toLowerCase())
                        );
                    }

                    // 状态过滤
                    if (this.trackStatusFilter) {
                        filtered = filtered.filter(item => item.status === this.trackStatusFilter);
                    }

                    // 分页处理
                    const start = (this.trackCurrentPage - 1) * this.trackPageSize;
                    const end = start + this.trackPageSize;
                    return filtered.slice(start, end);
                }
            },
            mounted() {
                this.initTrainOptions();
                this.initTrackMap();

                // 检查是否有传入的列车ID
                if (this.$route.query.trainId) {
                    this.selectedTrain = this.$route.query.trainId;
                    this.queryTrack();
                }
            },
            beforeDestroy() {
                this.stopPlayback();
            },
            methods: {
                initTrainOptions() {
                    this.trainOptions = [
                        { value: 'T1208', label: 'T1208 (1号线)' },
                        { value: 'T0537', label: 'T0537 (2号线)' },
                        { value: 'T0912', label: 'T0912 (4号线)' },
                        { value: 'T0315', label: 'T0315 (5号线)' },
                        { value: 'T0721', label: 'T0721 (6号线)' }
                    ];
                },
                onTrainChange() {
                    // 列车变更时清空轨迹数据
                    this.trackData = [];
                    this.trackStatistics = [];
                    this.stopPlayback();
                },
                onDateChange() {
                    // 日期变更时重新查询
                    if (this.selectedTrain) {
                        this.queryTrack();
                    }
                },
                queryTrack() {
                    if (!this.selectedTrain) {
                        this.$message.warning('请先选择列车');
                        return;
                    }

                    this.loading = true;
                    this.stopPlayback();

                    // 模拟加载轨迹数据
                    setTimeout(() => {
                        this.loading = false;
                        this.loadTrackData();
                        this.calculateTrackStatistics();
                        this.initTrackMap();
                        this.initSpeedAnalysisChart();

                        this.$message.success('轨迹数据加载完成');
                    }, 1500);
                },
                loadTrackData() {
                    // 根据选择的列车生成模拟轨迹数据
                    const baseData = {
                        'T1208': [
                            { time: '08:00:00', station: '西直门站', status: '进站', speed: '25km/h', passengers: '180人', temperature: '22°C', distance: '0km', duration: '0分钟', remarks: '正常进站' },
                            { time: '08:02:30', station: '西直门站', status: '停车', speed: '0km/h', passengers: '上车45人，下车32人', temperature: '22°C', distance: '0km', duration: '2分30秒', remarks: '正常停靠' },
                            { time: '08:05:00', station: '西直门站-大钟寺站', status: '正常运行', speed: '60km/h', passengers: '193人', temperature: '23°C', distance: '2.8km', duration: '5分钟', remarks: '区间运行' },
                            { time: '08:08:30', station: '大钟寺站', status: '进站', speed: '30km/h', passengers: '193人', temperature: '23°C', distance: '5.6km', duration: '8分30秒', remarks: '减速进站' },
                            { time: '08:10:00', station: '大钟寺站', status: '停车', speed: '0km/h', passengers: '上车28人，下车15人', temperature: '23°C', distance: '5.6km', duration: '10分钟', remarks: '正常停靠' },
                            { time: '08:12:30', station: '大钟寺站-知春路站', status: '正常运行', speed: '58km/h', passengers: '206人', temperature: '24°C', distance: '8.2km', duration: '12分30秒', remarks: '区间运行' },
                            { time: '08:15:00', station: '知春路站', status: '进站', speed: '28km/h', passengers: '206人', temperature: '24°C', distance: '10.8km', duration: '15分钟', remarks: '减速进站' },
                            { time: '08:17:00', station: '知春路站', status: '停车', speed: '0km/h', passengers: '上车35人，下车22人', temperature: '24°C', distance: '10.8km', duration: '17分钟', remarks: '正常停靠' },
                            { time: '08:19:30', station: '知春路站-五道口站', status: '正常运行', speed: '62km/h', passengers: '219人', temperature: '25°C', distance: '13.5km', duration: '19分30秒', remarks: '区间运行' },
                            { time: '08:22:00', station: '五道口站', status: '进站', speed: '32km/h', passengers: '219人', temperature: '25°C', distance: '16.2km', duration: '22分钟', remarks: '减速进站' },
                            { time: '08:24:00', station: '五道口站', status: '停车', speed: '0km/h', passengers: '上车42人，下车38人', temperature: '25°C', distance: '16.2km', duration: '24分钟', remarks: '正常停靠' },
                            { time: '08:26:30', station: '五道口站-上地站', status: '正常运行', speed: '65km/h', passengers: '223人', temperature: '26°C', distance: '19.1km', duration: '26分30秒', remarks: '区间运行' }
                        ],
                        'T0537': [
                            { time: '09:00:00', station: '朝阳门站', status: '进站', speed: '20km/h', passengers: '150人', temperature: '21°C', distance: '0km', duration: '0分钟', remarks: '减速进站' },
                            { time: '09:02:00', station: '朝阳门站', status: '停车', speed: '0km/h', passengers: '上车38人，下车25人', temperature: '21°C', distance: '0km', duration: '2分钟', remarks: '正常停靠' },
                            { time: '09:04:30', station: '朝阳门站-东四十条站', status: '减速运行', speed: '45km/h', passengers: '163人', temperature: '22°C', distance: '1.8km', duration: '4分30秒', remarks: '信号限速' },
                            { time: '09:07:00', station: '东四十条站', status: '进站', speed: '25km/h', passengers: '163人', temperature: '22°C', distance: '3.2km', duration: '7分钟', remarks: '减速进站' },
                            { time: '09:09:00', station: '东四十条站', status: '停车', speed: '0km/h', passengers: '上车32人，下车18人', temperature: '22°C', distance: '3.2km', duration: '9分钟', remarks: '正常停靠' },
                            { time: '09:11:30', station: '东四十条站-东直门站', status: '正常运行', speed: '55km/h', passengers: '177人', temperature: '23°C', distance: '5.8km', duration: '11分30秒', remarks: '区间运行' },
                            { time: '09:14:00', station: '东直门站', status: '进站', speed: '30km/h', passengers: '177人', temperature: '23°C', distance: '8.1km', duration: '14分钟', remarks: '减速进站' }
                        ],
                        'T0912': [
                            { time: '10:00:00', station: '北京南站', status: '出站', speed: '15km/h', passengers: '120人', temperature: '20°C', distance: '0km', duration: '0分钟', remarks: '正常出站' },
                            { time: '10:03:00', station: '北京南站-陶然亭站', status: '正常运行', speed: '58km/h', passengers: '120人', temperature: '20°C', distance: '2.1km', duration: '3分钟', remarks: '区间运行' },
                            { time: '10:06:00', station: '陶然亭站', status: '进站', speed: '28km/h', passengers: '120人', temperature: '21°C', distance: '4.5km', duration: '6分钟', remarks: '减速进站' },
                            { time: '10:08:00', station: '陶然亭站', status: '停车', speed: '0km/h', passengers: '上车25人，下车8人', temperature: '21°C', distance: '4.5km', duration: '8分钟', remarks: '正常停靠' },
                            { time: '10:10:30', station: '陶然亭站-菜市口站', status: '正常运行', speed: '60km/h', passengers: '137人', temperature: '21°C', distance: '6.8km', duration: '10分30秒', remarks: '区间运行' },
                            { time: '10:13:00', station: '菜市口站', status: '进站', speed: '32km/h', passengers: '137人', temperature: '22°C', distance: '9.2km', duration: '13分钟', remarks: '减速进站' }
                        ]
                    };

                    this.trackData = baseData[this.selectedTrain] || [];
                },
                calculateTrackStatistics() {
                    if (!this.trackData.length) {
                        this.trackStatistics = [];
                        return;
                    }

                    const speeds = this.trackData.map(item => parseFloat(item.speed.replace('km/h', ''))).filter(s => s > 0);
                    const maxSpeed = Math.max(...speeds);
                    const avgSpeed = speeds.length ? Math.round(speeds.reduce((a, b) => a + b, 0) / speeds.length) : 0;

                    const totalDistance = this.trackData.length ? parseFloat(this.trackData[this.trackData.length - 1].distance.replace('km', '')) : 0;
                    const totalDuration = this.trackData.length ? this.trackData[this.trackData.length - 1].duration : '0分钟';

                    const stationCount = this.trackData.filter(item => item.status === '停车').length;

                    this.trackStatistics = [
                        { label: '总距离', value: totalDistance + 'km', color: '#409EFF' },
                        { label: '总时长', value: totalDuration, color: '#67C23A' },
                        { label: '停靠站数', value: stationCount + '站', color: '#E6A23C' },
                        { label: '最高速度', value: maxSpeed + 'km/h', color: '#F56C6C' },
                        { label: '平均速度', value: avgSpeed + 'km/h', color: '#909399' },
                        { label: '当前状态', value: this.trackData.length ? this.trackData[this.trackData.length - 1].status : '无数据', color: '#606266' }
                    ];
                },
                playTrack() {
                    if (!this.trackData.length) {
                        this.$message.warning('没有轨迹数据可播放');
                        return;
                    }

                    this.isPlaying = true;
                    this.playbackProgress = 0;
                    this.startPlayback();
                },
                startPlayback() {
                    this.playbackInterval = setInterval(() => {
                        if (this.playbackProgress >= this.trackData.length - 1) {
                            this.stopPlayback();
                            this.$message.success('轨迹播放完成');
                            return;
                        }

                        this.playbackProgress++;
                        this.currentTrackPoint = this.trackData[this.playbackProgress];
                        this.updateMapPosition();
                    }, 1000 / this.playbackSpeed);
                },
                stopPlayback() {
                    this.isPlaying = false;
                    if (this.playbackInterval) {
                        clearInterval(this.playbackInterval);
                        this.playbackInterval = null;
                    }
                },
                playbackControl(action) {
                    switch (action) {
                        case 'start':
                            this.playbackProgress = 0;
                            this.currentTrackPoint = this.trackData[0];
                            this.updateMapPosition();
                            break;
                        case 'toggle':
                            if (this.isPlaying) {
                                this.stopPlayback();
                            } else {
                                this.startPlayback();
                            }
                            break;
                        case 'end':
                            this.playbackProgress = this.trackData.length - 1;
                            this.currentTrackPoint = this.trackData[this.playbackProgress];
                            this.updateMapPosition();
                            this.stopPlayback();
                            break;
                    }
                },
                onProgressChange(value) {
                    this.playbackProgress = value;
                    this.currentTrackPoint = this.trackData[value];
                    this.updateMapPosition();
                },
                updateMapPosition() {
                    // 更新地图上的列车位置
                    if (this.currentTrackPoint) {
                        console.log('更新地图位置:', this.currentTrackPoint);
                        // 实际项目中这里会调用地图API更新位置
                    }
                },
                initTrackMap() {
                    // 初始化轨迹地图
                    if (document.getElementById('track-map')) {
                        const mapContainer = document.getElementById('track-map');
                        mapContainer.innerHTML = `
                            <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f5f7fa; border-radius: 4px;">
                                <div style="text-align: center; color: #909399;">
                                    <i class="el-icon-map-location" style="font-size: 48px; margin-bottom: 10px; display: block;"></i>
                                    <p>列车运行轨迹地图</p>
                                    <p style="font-size: 12px;">实际项目中可集成百度地图、高德地图等</p>
                                    ${this.trackData.length ? `
                                        <div style="margin-top: 20px;">
                                            <div style="font-weight: bold; margin-bottom: 10px;">轨迹概览 - ${this.selectedTrain}</div>
                                            ${this.trackData.map((point, index) => `
                                                <div style="margin: 5px 0; padding: 5px 10px; background: #fff; border-radius: 4px; font-size: 12px; ${index === this.playbackProgress ? 'border: 2px solid #409EFF;' : ''}">
                                                    ${point.time} - ${point.station} (${point.speed})
                                                </div>
                                            `).join('')}
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        `;
                    }
                },
                initSpeedAnalysisChart() {
                    if (document.getElementById('speed-analysis-chart') && this.trackData.length) {
                        const chart = echarts.init(document.getElementById('speed-analysis-chart'));
                        const speeds = this.trackData.map(item => parseFloat(item.speed.replace('km/h', '')));
                        const times = this.trackData.map(item => item.time);

                        chart.setOption({
                            title: {
                                text: '速度变化',
                                textStyle: { fontSize: 14 }
                            },
                            tooltip: {
                                trigger: 'axis',
                                formatter: function(params) {
                                    const point = params[0];
                                    return `${point.name}<br/>速度: ${point.value}km/h`;
                                }
                            },
                            grid: {
                                left: '10%',
                                right: '10%',
                                bottom: '15%',
                                top: '20%'
                            },
                            xAxis: {
                                type: 'category',
                                data: times,
                                axisLabel: {
                                    rotate: 45,
                                    fontSize: 10
                                }
                            },
                            yAxis: {
                                type: 'value',
                                name: '速度(km/h)',
                                nameTextStyle: { fontSize: 10 }
                            },
                            series: [{
                                data: speeds,
                                type: 'line',
                                smooth: true,
                                itemStyle: { color: '#409EFF' },
                                areaStyle: {
                                    color: {
                                        type: 'linear',
                                        x: 0, y: 0, x2: 0, y2: 1,
                                        colorStops: [
                                            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                                            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                                        ]
                                    }
                                }
                            }]
                        });
                    }
                },
                fullscreenMap() {
                    this.$alert('全屏地图功能开发中...', '提示', {
                        confirmButtonText: '确定'
                    });
                },
                clearTrack() {
                    this.$confirm('确定要清空当前轨迹数据吗？', '确认清空', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        this.trackData = [];
                        this.trackStatistics = [];
                        this.stopPlayback();
                        this.playbackProgress = 0;
                        this.currentTrackPoint = null;
                        this.initTrackMap();
                        this.$message.success('轨迹数据已清空');
                    }).catch(() => {
                        this.$message.info('已取消清空操作');
                    });
                },
                exportTrackData() {
                    if (!this.trackData.length) {
                        this.$message.warning('没有轨迹数据可导出');
                        return;
                    }

                    const data = this.trackData.map(item => ({
                        '时间': item.time,
                        '站点/位置': item.station,
                        '状态': item.status,
                        '速度': item.speed,
                        '乘客数量': item.passengers,
                        '温度': item.temperature,
                        '累计距离': item.distance,
                        '运行时长': item.duration,
                        '备注': item.remarks
                    }));

                    const csvContent = this.convertToCSV(data);
                    this.downloadCSV(csvContent, `列车${this.selectedTrain}运行轨迹_${this.formatDate(this.dateValue)}.csv`);

                    this.$message.success('轨迹数据导出成功');
                },
                convertToCSV(data) {
                    if (!data.length) return '';

                    const headers = Object.keys(data[0]);
                    const csvRows = [headers.join(',')];

                    data.forEach(row => {
                        const values = headers.map(header => {
                            const value = row[header];
                            return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                        });
                        csvRows.push(values.join(','));
                    });

                    return '\uFEFF' + csvRows.join('\n'); // 添加BOM以支持中文
                },
                downloadCSV(content, filename) {
                    const blob = new Blob([content], { type: 'text/csv;charset=utf-8' });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                },
                formatDate(date) {
                    if (!date) return '';
                    const d = new Date(date);
                    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
                },
                getSpeedColor(speed) {
                    const speedValue = parseFloat(speed.replace('km/h', ''));
                    if (speedValue >= 50) return '#67C23A';
                    if (speedValue >= 30) return '#E6A23C';
                    if (speedValue === 0) return '#909399';
                    return '#F56C6C';
                },
                getStatusType(status) {
                    const typeMap = {
                        '正常运行': 'success',
                        '进站': 'warning',
                        '出站': 'primary',
                        '停车': 'info',
                        '减速运行': 'warning'
                    };
                    return typeMap[status] || 'info';
                },
                getTemperatureColor(temperature) {
                    const tempValue = parseFloat(temperature.replace('°C', ''));
                    if (tempValue >= 18 && tempValue <= 26) return '#67C23A';
                    if (tempValue >= 15 && tempValue <= 30) return '#E6A23C';
                    return '#F56C6C';
                },
                viewTrackDetail(trackPoint) {
                    this.$alert(`
                        <div style="text-align: left;">
                            <h4>轨迹点详情</h4>
                            <p><strong>时间:</strong> ${trackPoint.time}</p>
                            <p><strong>位置:</strong> ${trackPoint.station}</p>
                            <p><strong>状态:</strong> ${trackPoint.status}</p>
                            <p><strong>速度:</strong> ${trackPoint.speed}</p>
                            <p><strong>乘客:</strong> ${trackPoint.passengers}</p>
                            <p><strong>温度:</strong> ${trackPoint.temperature}</p>
                            <p><strong>距离:</strong> ${trackPoint.distance}</p>
                            <p><strong>时长:</strong> ${trackPoint.duration}</p>
                            <p><strong>备注:</strong> ${trackPoint.remarks}</p>
                        </div>
                    `, '轨迹详情', {
                        confirmButtonText: '确定',
                        dangerouslyUseHTMLString: true
                    });
                },
                locateOnMap(trackPoint) {
                    this.$message.info(`定位到: ${trackPoint.station} (${trackPoint.time})`);
                    // 实际项目中这里会在地图上定位到指定位置
                },
                handleTrackSizeChange(val) {
                    this.trackPageSize = val;
                    this.trackCurrentPage = 1;
                },
                handleTrackCurrentChange(val) {
                    this.trackCurrentPage = val;
                }
            },
            watch: {
                playbackSpeed() {
                    if (this.isPlaying) {
                        this.stopPlayback();
                        this.startPlayback();
                    }
                }
            }
        }
    },
    {
        path: '/warning',
        component: {
            template: '#warning-template',
            data() {
                return {
                    loading: true,
                    warningList: [],
                    searchQuery: '',
                    filterLevel: '',
                    filterStatus: '',
                    dateRange: ''
                };
            },
            computed: {
                filteredWarnings() {
                    return this.warningList.filter(warning => {
                        const matchQuery = warning.id.toLowerCase().includes(this.searchQuery.toLowerCase()) || 
                                          warning.trainId.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                                          warning.type.toLowerCase().includes(this.searchQuery.toLowerCase());
                        const matchLevel = this.filterLevel ? warning.level === this.filterLevel : true;
                        const matchStatus = this.filterStatus ? warning.status === this.filterStatus : true;
                        return matchQuery && matchLevel && matchStatus;
                    });
                }
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.warningList = [
                        { id: 'W2023112201', trainId: 'T1208', type: '温度异常', level: '中', time: '2023-11-22 08:32:15', location: '西单站-复兴门站', description: '电机温度超过正常范围', status: '已处理', handler: '张工', handleTime: '2023-11-22 09:15:22' },
                        { id: 'W2023112105', trainId: 'T0537', type: '制动系统异常', level: '高', time: '2023-11-21 15:47:23', location: '朝阳门站-东四十条站', description: '制动系统压力异常波动', status: '处理中', handler: '李工', handleTime: '-' },
                        { id: 'W2023112003', trainId: 'T0912', type: '信号干扰', level: '低', time: '2023-11-20 10:15:42', location: '北京南站-陶然亭站', description: '短暂信号干扰，已自动恢复', status: '已处理', handler: '系统', handleTime: '2023-11-20 10:16:35' },
                        { id: 'W2023111908', trainId: 'T0315', type: '车门故障', level: '中', time: '2023-11-19 18:22:37', location: '惠新西街南口站', description: '3号车门关闭异常', status: '已处理', handler: '王工', handleTime: '2023-11-19 18:40:12' },
                        { id: 'W2023111805', trainId: 'T0721', type: '供电系统异常', level: '高', time: '2023-11-18 09:05:18', location: '草房站-常营站', description: '供电系统电压波动', status: '已处理', handler: '赵工', handleTime: '2023-11-18 09:30:45' }
                    ];
                }, 1000);
            },
            methods: {
                getWarningLevelClass(level) {
                    if (level === '高') return 'warning-level-high';
                    if (level === '中') return 'warning-level-medium';
                    if (level === '低') return 'warning-level-low';
                    return '';
                },
                viewWarningDetail(warning) {
                    // 查看故障详情
                    console.log('查看故障详情:', warning);
                },
                handleWarning(warning) {
                    // 处理故障
                    console.log('处理故障:', warning);
                }
            }
        }
    },
    // 添加车辆调度管理路由
    {
        path: '/dispatch',
        component: {
            template: '#dispatch-template',
            data() {
                return {
                    loading: true,
                    dispatchList: [],
                    searchQuery: '',
                    filterLine: '',
                    filterStatus: '',
                    dateValue: '',
                    detailDialogVisible: false,
                    formDialogVisible: false,
                    dialogTitle: '添加调度',
                    currentDispatch: null,
                    dispatchForm: {
                        id: '',
                        trainId: '',
                        line: '',
                        driver: '',
                        status: '待发车',
                        departureTime: '',
                        arrivalTime: '',
                        startStation: '',
                        endStation: '',
                        viaStations: [],
                        notes: '',
                        createTime: ''
                    },
                    dispatchRules: {
                        trainId: [{ required: true, message: '请选择列车', trigger: 'change' }],
                        line: [{ required: true, message: '请选择线路', trigger: 'change' }],
                        driver: [{ required: true, message: '请选择司机', trigger: 'change' }],
                        departureTime: [{ required: true, message: '请选择发车时间', trigger: 'change' }],
                        startStation: [{ required: true, message: '请选择起始站', trigger: 'change' }],
                        endStation: [{ required: true, message: '请选择终点站', trigger: 'change' }]
                    },
                    availableTrains: [
                        { value: 'T1208', label: 'T1208' },
                        { value: 'T0537', label: 'T0537' },
                        { value: 'T0912', label: 'T0912' },
                        { value: 'T0315', label: 'T0315' },
                        { value: 'T0628', label: 'T0628' }
                    ],
                    availableDrivers: [
                        { value: '张明', label: '张明' },
                        { value: '李强', label: '李强' },
                        { value: '王伟', label: '王伟' },
                        { value: '赵勇', label: '赵勇' },
                        { value: '刘洋', label: '刘洋' }
                    ],
                    stationOptions: [],
                    endStationOptions: [],
                    viaStationOptions: [],
                    stationMap: {
                        '1号线': [
                            { value: '苹果园站', label: '苹果园站' },
                            { value: '古城站', label: '古城站' },
                            { value: '八角游乐园站', label: '八角游乐园站' },
                            { value: '八宝山站', label: '八宝山站' },
                            { value: '玉泉路站', label: '玉泉路站' },
                            { value: '五棵松站', label: '五棵松站' },
                            { value: '万寿路站', label: '万寿路站' },
                            { value: '公主坟站', label: '公主坟站' },
                            { value: '军事博物馆站', label: '军事博物馆站' },
                            { value: '木樨地站', label: '木樨地站' },
                            { value: '南礼士路站', label: '南礼士路站' },
                            { value: '复兴门站', label: '复兴门站' },
                            { value: '西单站', label: '西单站' },
                            { value: '天安门西站', label: '天安门西站' },
                            { value: '天安门东站', label: '天安门东站' },
                            { value: '王府井站', label: '王府井站' },
                            { value: '东单站', label: '东单站' },
                            { value: '建国门站', label: '建国门站' },
                            { value: '永安里站', label: '永安里站' },
                            { value: '国贸站', label: '国贸站' },
                            { value: '大望路站', label: '大望路站' }
                        ],
                        '2号线': [
                            { value: '西直门站', label: '西直门站' },
                            { value: '积水潭站', label: '积水潭站' },
                            { value: '鼓楼大街站', label: '鼓楼大街站' },
                            { value: '安定门站', label: '安定门站' },
                            { value: '雍和宫站', label: '雍和宫站' },
                            { value: '东直门站', label: '东直门站' },
                            { value: '东四十条站', label: '东四十条站' },
                            { value: '朝阳门站', label: '朝阳门站' },
                            { value: '建国门站', label: '建国门站' },
                            { value: '北京站站', label: '北京站站' },
                            { value: '崇文门站', label: '崇文门站' },
                            { value: '前门站', label: '前门站' },
                            { value: '和平门站', label: '和平门站' },
                            { value: '宣武门站', label: '宣武门站' },
                            { value: '长椿街站', label: '长椿街站' },
                            { value: '复兴门站', label: '复兴门站' },
                            { value: '阜成门站', label: '阜成门站' },
                            { value: '车公庄站', label: '车公庄站' }
                        ],
                        '4号线': [
                            { value: '安河桥北站', label: '安河桥北站' },
                            { value: '北宫门站', label: '北宫门站' },
                            { value: '西苑站', label: '西苑站' },
                            { value: '圆明园站', label: '圆明园站' },
                            { value: '北京大学东门站', label: '北京大学东门站' },
                            { value: '中关村站', label: '中关村站' },
                            { value: '海淀黄庄站', label: '海淀黄庄站' },
                            { value: '人民大学站', label: '人民大学站' },
                            { value: '魏公村站', label: '魏公村站' },
                            { value: '国家图书馆站', label: '国家图书馆站' },
                            { value: '动物园站', label: '动物园站' },
                            { value: '西直门站', label: '西直门站' },
                            { value: '新街口站', label: '新街口站' },
                            { value: '平安里站', label: '平安里站' },
                            { value: '西四站', label: '西四站' },
                            { value: '灵境胡同站', label: '灵境胡同站' },
                            { value: '西单站', label: '西单站' },
                            { value: '宣武门站', label: '宣武门站' },
                            { value: '菜市口站', label: '菜市口站' },
                            { value: '陶然亭站', label: '陶然亭站' },
                            { value: '北京南站站', label: '北京南站站' }
                        ],
                        '5号线': [
                            { value: '天通苑北站', label: '天通苑北站' },
                            { value: '天通苑站', label: '天通苑站' },
                            { value: '天通苑南站', label: '天通苑南站' },
                            { value: '立水桥站', label: '立水桥站' },
                            { value: '立水桥南站', label: '立水桥南站' },
                            { value: '北苑路北站', label: '北苑路北站' },
                            { value: '大屯路东站', label: '大屯路东站' },
                            { value: '惠新西街北口站', label: '惠新西街北口站' },
                            { value: '惠新西街南口站', label: '惠新西街南口站' },
                            { value: '和平西桥站', label: '和平西桥站' },
                            { value: '和平里北街站', label: '和平里北街站' },
                            { value: '雍和宫站', label: '雍和宫站' },
                            { value: '北新桥站', label: '北新桥站' },
                            { value: '张自忠路站', label: '张自忠路站' },
                            { value: '东四站', label: '东四站' },
                            { value: '灯市口站', label: '灯市口站' },
                            { value: '东单站', label: '东单站' },
                            { value: '崇文门站', label: '崇文门站' },
                            { value: '磁器口站', label: '磁器口站' },
                            { value: '天坛东门站', label: '天坛东门站' },
                            { value: '蒲黄榆站', label: '蒲黄榆站' }
                        ],
                        '6号线': [
                            { value: '海淀五路居站', label: '海淀五路居站' },
                            { value: '慈寿寺站', label: '慈寿寺站' },
                            { value: '花园桥站', label: '花园桥站' },
                            { value: '白石桥南站', label: '白石桥南站' },
                            { value: '车公庄西站', label: '车公庄西站' },
                            { value: '车公庄站', label: '车公庄站' },
                            { value: '平安里站', label: '平安里站' },
                            { value: '北海北站', label: '北海北站' },
                            { value: '南锣鼓巷站', label: '南锣鼓巷站' },
                            { value: '东四站', label: '东四站' },
                            { value: '朝阳门站', label: '朝阳门站' },
                            { value: '东大桥站', label: '东大桥站' },
                            { value: '呼家楼站', label: '呼家楼站' },
                            { value: '金台路站', label: '金台路站' },
                            { value: '十里堡站', label: '十里堡站' },
                            { value: '青年路站', label: '青年路站' },
                            { value: '褡裢坡站', label: '褡裢坡站' },
                            { value: '黄渠站', label: '黄渠站' },
                            { value: '常营站', label: '常营站' },
                            { value: '草房站', label: '草房站' }
                        ]
                    }
                };
            },
            computed: {
                filteredDispatch() {
                    return this.dispatchList.filter(dispatch => {
                        const matchQuery = dispatch.id.toLowerCase().includes(this.searchQuery.toLowerCase()) || 
                                          dispatch.trainId.toLowerCase().includes(this.searchQuery.toLowerCase());
                        const matchLine = this.filterLine ? dispatch.line === this.filterLine : true;
                        const matchStatus = this.filterStatus ? dispatch.status === this.filterStatus : true;
                        
                        // 如果选择了日期，则过滤出当天的调度
                        let matchDate = true;
                        if (this.dateValue) {
                            const selectedDate = new Date(this.dateValue);
                            const dispatchDate = new Date(dispatch.departureTime);
                            matchDate = selectedDate.toDateString() === dispatchDate.toDateString();
                        }
                        
                        return matchQuery && matchLine && matchStatus && matchDate;
                    });
                }
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.dispatchList = [
                        { 
                            id: 'D20231122001', 
                            trainId: 'T1208', 
                            line: '1号线', 
                            driver: '张明', 
                            status: '运行中', 
                            departureTime: '2023-11-22 08:00:00', 
                            arrivalTime: '2023-11-22 09:30:00', 
                            startStation: '苹果园站', 
                            endStation: '国贸站',
                            viaStations: '古城站,八角游乐园站,八宝山站,玉泉路站,五棵松站,万寿路站,公主坟站',
                            createTime: '2023-11-21 16:30:00',
                            waitingTime: '2023-11-22 07:45:00'
                        },
                        { 
                            id: 'D20231122002', 
                            trainId: 'T0537', 
                            line: '2号线', 
                            driver: '李强', 
                            status: '待发车', 
                            departureTime: '2023-11-22 10:15:00', 
                            arrivalTime: '2023-11-22 11:45:00', 
                            startStation: '西直门站', 
                            endStation: '朝阳门站',
                            viaStations: '积水潭站,鼓楼大街站,安定门站,雍和宫站,东直门站,东四十条站',
                            createTime: '2023-11-21 17:20:00'
                        },
                        { 
                            id: 'D20231122003', 
                            trainId: 'T0912', 
                            line: '4号线', 
                            driver: '王伟', 
                            status: '已完成', 
                            departureTime: '2023-11-22 06:30:00', 
                            arrivalTime: '2023-11-22 08:00:00', 
                            startStation: '安河桥北站', 
                            endStation: '北京南站站',
                            viaStations: '北宫门站,西苑站,圆明园站,北京大学东门站,中关村站',
                            createTime: '2023-11-21 15:00:00',
                            waitingTime: '2023-11-22 06:15:00',
                            actualArrivalTime: '2023-11-22 07:58:00'
                        },
                        { 
                            id: 'D20231122004', 
                            trainId: 'T0315', 
                            line: '5号线', 
                            driver: '赵勇', 
                            status: '延误', 
                            departureTime: '2023-11-22 09:00:00', 
                            arrivalTime: '2023-11-22 10:30:00', 
                            startStation: '天通苑北站', 
                            endStation: '蒲黄榆站',
                            viaStations: '天通苑站,天通苑南站,立水桥站,立水桥南站',
                            createTime: '2023-11-21 16:00:00',
                            waitingTime: '2023-11-22 08:45:00'
                        },
                        { 
                            id: 'D20231122005', 
                            trainId: 'T0628', 
                            line: '6号线', 
                            driver: '刘洋', 
                            status: '已发车', 
                            departureTime: '2023-11-22 07:30:00', 
                            arrivalTime: '2023-11-22 09:00:00', 
                            startStation: '海淀五路居站', 
                            endStation: '草房站',
                            viaStations: '慈寿寺站,花园桥站,白石桥南站,车公庄西站',
                            createTime: '2023-11-21 14:30:00',
                            waitingTime: '2023-11-22 07:15:00'
                        }
                    ];
                }, 1000);
            },
            methods: {
                getStatusClass(status) {
                    if (status === '正常运行' || status === '运行中' || status === '已完成') return 'status-normal';
                    if (status === '减速运行' || status === '待发车' || status === '已发车') return 'status-warning';
                    if (status === '临时停车' || status === '延误' || status === '已取消') return 'status-danger';
                    return '';
                },
                viewDispatchDetail(dispatch) {
                    this.currentDispatch = JSON.parse(JSON.stringify(dispatch));
                    this.detailDialogVisible = true;
                },
                editDispatch(dispatch) {
                    this.dialogTitle = '编辑调度';
                    this.dispatchForm = JSON.parse(JSON.stringify(dispatch));
                    
                    // 将途经站点字符串转换为数组
                    if (typeof this.dispatchForm.viaStations === 'string') {
                        this.dispatchForm.viaStations = this.dispatchForm.viaStations.split(',');
                    }
                    
                    // 设置站点选项
                    this.stationOptions = this.stationMap[this.dispatchForm.line] || [];
                    this.updateEndStations();
                    this.updateViaStations();
                    
                    this.detailDialogVisible = false;
                    this.formDialogVisible = true;
                },
                addNewDispatch() {
                    this.dialogTitle = '添加调度';
                    this.dispatchForm = {
                        id: 'D' + new Date().getTime(),
                        trainId: '',
                        line: '',
                        driver: '',
                        status: '待发车',
                        departureTime: '',
                        arrivalTime: '',
                        startStation: '',
                        endStation: '',
                        viaStations: [],
                        notes: '',
                        createTime: new Date().toLocaleString()
                    };
                    this.formDialogVisible = true;
                },
                submitDispatchForm() {
                    this.$refs.dispatchForm.validate((valid) => {
                        if (valid) {
                            // 将途经站点数组转换为字符串保存
                            const formData = JSON.parse(JSON.stringify(this.dispatchForm));
                            if (Array.isArray(formData.viaStations)) {
                                formData.viaStations = formData.viaStations.join(',');
                            }
                            
                            // 查找是否已存在该ID的调度
                            const index = this.dispatchList.findIndex(item => item.id === formData.id);
                            
                            if (index !== -1) {
                                // 更新现有调度
                                this.$set(this.dispatchList, index, formData);
                                this.$message({
                                    message: '调度信息已更新',
                                    type: 'success'
                                });
                            } else {
                                // 添加新调度
                                this.dispatchList.unshift(formData);
                                this.$message({
                                    message: '调度已添加',
                                    type: 'success'
                                });
                            }
                            
                            this.formDialogVisible = false;
                        } else {
                            return false;
                        }
                    });
                },
                handleSizeChange(val) {
                    console.log(`每页 ${val} 条`);
                },
                handleCurrentChange(val) {
                    console.log(`当前页: ${val}`);
                },
                getStepActive() {
                    const statusMap = {
                        '待发车': 1,
                        '已发车': 2,
                        '运行中': 3,
                        '已完成': 4,
                        '已取消': 1,
                        '延误': 2
                    };
                    return statusMap[this.currentDispatch.status] || 0;
                },
                updateEndStations() {
                    if (!this.dispatchForm.line || !this.dispatchForm.startStation) {
                        this.endStationOptions = [];
                        return;
                    }
                    
                    // 获取当前线路的所有站点
                    const allStations = this.stationMap[this.dispatchForm.line] || [];
                    
                    // 过滤掉起始站
                    this.endStationOptions = allStations.filter(station => 
                        station.value !== this.dispatchForm.startStation
                    );
                    
                    this.updateViaStations();
                },
                updateViaStations() {
                    if (!this.dispatchForm.line || !this.dispatchForm.startStation || !this.dispatchForm.endStation) {
                        this.viaStationOptions = [];
                        return;
                    }
                    
                    // 获取当前线路的所有站点
                    const allStations = this.stationMap[this.dispatchForm.line] || [];
                    
                    // 过滤掉起始站和终点站
                    this.viaStationOptions = allStations.filter(station => 
                        station.value !== this.dispatchForm.startStation && 
                        station.value !== this.dispatchForm.endStation
                    );
                }
            },
            watch: {
                'dispatchForm.line'() {
                    // 当线路变化时，更新站点选项
                    this.stationOptions = this.stationMap[this.dispatchForm.line] || [];
                    this.dispatchForm.startStation = '';
                    this.dispatchForm.endStation = '';
                    this.dispatchForm.viaStations = [];
                    this.endStationOptions = [];
                    this.viaStationOptions = [];
                }
            }
        }
    },
    // 添加运行数据分析路由
    {
        path: '/data-analysis',
        component: {
            template: '#data-analysis-template',
            data() {
                return {
                    loading: true,
                    analysisData: {
                        dailyPassengers: [],
                        lineEfficiency: [],
                        energyConsumption: [],
                        peakHourData: []
                    },
                    timeRange: 'week',
                    selectedLine: '',
                    dateRange: ''
                };
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.loadAnalysisData();
                }, 1000);
            },
            methods: {
                loadAnalysisData() {
                    // 模拟加载分析数据
                    this.analysisData = {
                        dailyPassengers: [
                            { date: '11-16', count: 1250000, increase: 2.5 },
                            { date: '11-17', count: 1320000, increase: 5.6 },
                            { date: '11-18', count: 980000, increase: -25.8 },
                            { date: '11-19', count: 850000, increase: -13.3 },
                            { date: '11-20', count: 1380000, increase: 62.4 },
                            { date: '11-21', count: 1420000, increase: 2.9 },
                            { date: '11-22', count: 1380000, increase: -2.8 }
                        ],
                        lineEfficiency: [
                            { line: '1号线', efficiency: 92.5, punctuality: 96.8, loadFactor: 85.3 },
                            { line: '2号线', efficiency: 91.8, punctuality: 95.2, loadFactor: 87.2 },
                            { line: '4号线', efficiency: 93.2, punctuality: 97.5, loadFactor: 82.5 },
                            { line: '5号线', efficiency: 94.5, punctuality: 98.1, loadFactor: 88.7 },
                            { line: '6号线', efficiency: 90.8, punctuality: 94.7, loadFactor: 86.4 }
                        ],
                        energyConsumption: [
                            { date: '11-16', consumption: 258000, perKm: 42.5 },
                            { date: '11-17', consumption: 262000, perKm: 43.2 },
                            { date: '11-18', consumption: 245000, perKm: 40.3 },
                            { date: '11-19', consumption: 228000, perKm: 37.5 },
                            { date: '11-20', consumption: 267000, perKm: 44.1 },
                            { date: '11-21', consumption: 271000, perKm: 44.7 },
                            { date: '11-22', consumption: 265000, perKm: 43.8 }
                        ],
                        peakHourData: [
                            { hour: '7:00', passengers: 125000 },
                            { hour: '8:00', passengers: 185000 },
                            { hour: '9:00', passengers: 142000 },
                            { hour: '17:00', passengers: 158000 },
                            { hour: '18:00', passengers: 175000 },
                            { hour: '19:00', passengers: 132000 }
                        ]
                    };
                    this.initCharts();
                },
                initCharts() {
                    // 初始化客流量统计图表
                    const passengerChart = echarts.init(document.getElementById('passenger-chart'));
                    passengerChart.setOption({
                        title: {
                            text: '日客流量统计',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis',
                            formatter: '{b}<br/>客流量: {c}人次<br/>环比: {d}%'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.analysisData.dailyPassengers.map(item => item.date)
                        },
                        yAxis: {
                            type: 'value',
                            name: '人次',
                            axisLabel: {
                                formatter: value => {
                                    if (value >= 1000000) {
                                        return (value / 1000000).toFixed(1) + 'M';
                                    } else if (value >= 1000) {
                                        return (value / 1000).toFixed(0) + 'K';
                                    }
                                    return value;
                                }
                            }
                        },
                        series: [{
                            data: this.analysisData.dailyPassengers.map(item => item.count),
                            type: 'bar',
                            itemStyle: {
                                color: '#409EFF'
                            }
                        }]
                    });

                    // 初始化线路效率对比图表
                    const efficiencyChart = echarts.init(document.getElementById('efficiency-comparison-chart'));
                    efficiencyChart.setOption({
                        title: {
                            text: '线路效率对比',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        legend: {
                            data: ['运行效率', '准点率', '满载率'],
                            top: 'bottom'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.analysisData.lineEfficiency.map(item => item.line)
                        },
                        yAxis: {
                            type: 'value',
                            name: '百分比(%)',
                            min: 75
                        },
                        series: [
                            {
                                name: '运行效率',
                                type: 'bar',
                                data: this.analysisData.lineEfficiency.map(item => item.efficiency)
                            },
                            {
                                name: '准点率',
                                type: 'bar',
                                data: this.analysisData.lineEfficiency.map(item => item.punctuality)
                            },
                            {
                                name: '满载率',
                                type: 'bar',
                                data: this.analysisData.lineEfficiency.map(item => item.loadFactor)
                            }
                        ]
                    });

                    // 初始化能耗分析图表
                    const energyChart = echarts.init(document.getElementById('energy-chart'));
                    energyChart.setOption({
                        title: {
                            text: '能耗分析',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'cross'
                            }
                        },
                        legend: {
                            data: ['总能耗', '每公里能耗'],
                            top: 'bottom'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.analysisData.energyConsumption.map(item => item.date)
                        },
                        yAxis: [
                            {
                                type: 'value',
                                name: '总能耗(kWh)',
                                position: 'left'
                            },
                            {
                                type: 'value',
                                name: '每公里能耗(kWh/km)',
                                position: 'right'
                            }
                        ],
                        series: [
                            {
                                name: '总能耗',
                                type: 'bar',
                                data: this.analysisData.energyConsumption.map(item => item.consumption)
                            },
                            {
                                name: '每公里能耗',
                                type: 'line',
                                yAxisIndex: 1,
                                data: this.analysisData.energyConsumption.map(item => item.perKm)
                            }
                        ]
                    });

                    // 初始化高峰时段客流图表
                    const peakHourChart = echarts.init(document.getElementById('peak-hour-chart'));
                    peakHourChart.setOption({
                        title: {
                            text: '高峰时段客流分析',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.analysisData.peakHourData.map(item => item.hour)
                        },
                        yAxis: {
                            type: 'value',
                            name: '人次',
                            axisLabel: {
                                formatter: value => {
                                    if (value >= 1000000) {
                                        return (value / 1000000).toFixed(1) + 'M';
                                    } else if (value >= 1000) {
                                        return (value / 1000).toFixed(0) + 'K';
                                    }
                                    return value;
                                }
                            }
                        },
                        series: [{
                            data: this.analysisData.peakHourData.map(item => item.passengers),
                            type: 'line',
                            smooth: true,
                            areaStyle: {},
                            itemStyle: {
                                color: '#67C23A'
                            }
                        }]
                    });

                    // 监听窗口大小变化，重新调整图表大小
                    window.addEventListener('resize', function() {
                        passengerChart.resize();
                        efficiencyChart.resize();
                        energyChart.resize();
                        peakHourChart.resize();
                    });
                },
                changeTimeRange(range) {
                    this.timeRange = range;
                    this.loading = true;
                    // 模拟数据加载
                    setTimeout(() => {
                        this.loading = false;
                        this.loadAnalysisData();
                    }, 500);
                },
                exportData(type) {
                    // 导出数据
                    console.log('导出数据:', type);
                }
            }
        }
    },
    // 添加安全评估路由
    {
        path: '/safety',
        component: {
            template: '#safety-template',
            data() {
                return {
                    loading: true,
                    selectedPeriod: 'week',
                    activeTab: 'equipment',
                    safetyScore: 0,
                    safetyIndicators: [],
                    equipmentSafetyScore: 0,
                    operationSafetyScore: 0,
                    personnelSafetyScore: 0,
                    equipmentSafetyData: [],
                    operationSafetyData: [],
                    personnelSafetyData: [],
                    safetyIncidents: [],
                    riskWarnings: [],
                    safetyTrends: [],
                    incidentStatistics: {
                        totalIncidents: 0,
                        resolvedIncidents: 0,
                        pendingIncidents: 0,
                        avgResolutionTime: 0,
                        incidentsByType: {},
                        incidentsByLevel: {},
                        monthlyTrend: []
                    },
                    incidentDialogVisible: false,
                    incidentFormDialogVisible: false,
                    incidentDialogTitle: '添加安全事件',
                    currentIncident: null,
                    incidentForm: {
                        id: '',
                        time: '',
                        type: '',
                        level: '',
                        location: '',
                        description: '',
                        impact: '',
                        measures: '',
                        status: '待处理',
                        responsible: '',
                        recorder: ''
                    },
                    incidentRules: {
                        time: [{ required: true, message: '请选择发生时间', trigger: 'change' }],
                        type: [{ required: true, message: '请选择事件类型', trigger: 'change' }],
                        level: [{ required: true, message: '请选择事件级别', trigger: 'change' }],
                        location: [{ required: true, message: '请输入发生位置', trigger: 'blur' }],
                        description: [{ required: true, message: '请输入事件描述', trigger: 'blur' }]
                    }
                };
            },
            mounted() {
                this.loadSafetyData();
                // 监听窗口大小变化
                window.addEventListener('resize', this.resizeCharts);
            },
            beforeDestroy() {
                // 组件销毁前停止风险监测
                this.stopRiskMonitoring();
                // 移除窗口大小变化监听器
                window.removeEventListener('resize', this.resizeCharts);
            },
            methods: {
                loadSafetyData() {
                    this.loading = true;
                    // 模拟数据加载
                    setTimeout(() => {
                        this.loading = false;
                        this.initSafetyData();
                        this.calculateSafetyScore();
                        this.calculateIncidentStatistics(); // 计算事件统计
                        this.initSafetyCharts();
                        this.startRiskMonitoring(); // 启动风险监测
                    }, 1000);
                },
                initSafetyData() {
                    // 初始化安全指标数据
                    this.safetyIndicators = [
                        { title: '设备安全', score: 94, trend: 'up' },
                        { title: '运行安全', score: 96, trend: 'up' },
                        { title: '人员安全', score: 92, trend: 'down' },
                        { title: '环境安全', score: 98, trend: 'stable' },
                        { title: '信息安全', score: 89, trend: 'up' },
                        { title: '应急响应', score: 91, trend: 'stable' }
                    ];

                    // 设备安全数据
                    this.equipmentSafetyData = [
                        { category: '信号系统', checkItems: 45, passRate: 97.8, score: 95, issues: '2个轻微故障已修复', status: '正常' },
                        { category: '动力系统', checkItems: 38, passRate: 94.7, score: 92, issues: '1个中等故障处理中', status: '注意' },
                        { category: '制动系统', checkItems: 42, passRate: 98.1, score: 96, issues: '无异常', status: '正常' },
                        { category: '车门系统', checkItems: 28, passRate: 96.4, score: 94, issues: '定期维护中', status: '正常' },
                        { category: '空调系统', checkItems: 35, passRate: 95.2, score: 93, issues: '1个轻微故障', status: '正常' },
                        { category: '监控系统', checkItems: 52, passRate: 99.0, score: 98, issues: '运行良好', status: '优秀' }
                    ];

                    // 运行安全数据
                    this.operationSafetyData = [
                        { category: '运行速度', standard: '≤80km/h', actual: '76.5km/h', deviation: -4.4, score: 96, suggestion: '保持当前运行状态' },
                        { category: '制动距离', standard: '≤150m', actual: '142m', deviation: -5.3, score: 95, suggestion: '制动性能良好' },
                        { category: '信号响应', standard: '≤2s', actual: '1.8s', deviation: -10.0, score: 98, suggestion: '响应时间优秀' },
                        { category: '停车精度', standard: '±0.5m', actual: '±0.3m', deviation: -40.0, score: 97, suggestion: '停车精度很高' },
                        { category: '发车间隔', standard: '120s', actual: '118s', deviation: -1.7, score: 94, suggestion: '间隔控制良好' },
                        { category: '准点率', standard: '≥95%', actual: '96.8%', deviation: 1.9, score: 96, suggestion: '准点率达标' }
                    ];

                    // 人员安全数据
                    this.personnelSafetyData = [
                        { category: '安全培训', standard: '每月不少于8小时', completion: 95, score: 95, notes: '本月已完成7.6小时' },
                        { category: '健康检查', standard: '每季度体检', completion: 88, score: 88, notes: '2人延期体检' },
                        { category: '应急演练', standard: '每月1次', completion: 100, score: 100, notes: '本月已完成演练' },
                        { category: '违规记录', standard: '0起', completion: 92, score: 92, notes: '本月2起轻微违规' },
                        { category: '资质认证', standard: '100%持证上岗', completion: 98, score: 98, notes: '1人证书即将到期' },
                        { category: '安全意识', standard: '考核≥90分', completion: 94, score: 94, notes: '平均分93.2分' }
                    ];

                    // 安全事件数据
                    this.safetyIncidents = [
                        {
                            id: 'SI2023112201',
                            time: '2023-11-22 14:30:00',
                            type: '设备故障',
                            level: '中',
                            location: '1号线西单站',
                            description: '车门传感器异常导致车门无法正常关闭',
                            impact: '列车延误5分钟，影响后续3班列车',
                            measures: '更换传感器，调整运行计划',
                            status: '已处理',
                            handler: '张工',
                            handleTime: '2023-11-22 15:15:00'
                        },
                        {
                            id: 'SI2023112105',
                            time: '2023-11-21 09:15:00',
                            type: '人员违规',
                            level: '低',
                            location: '2号线调度室',
                            description: '司机未按规定进行发车前检查',
                            impact: '无直接影响，存在安全隐患',
                            measures: '对司机进行安全教育，加强监督',
                            status: '已处理',
                            handler: '李主管',
                            handleTime: '2023-11-21 10:30:00'
                        },
                        {
                            id: 'SI2023112003',
                            time: '2023-11-20 16:45:00',
                            type: '环境异常',
                            level: '高',
                            location: '4号线隧道区间',
                            description: '隧道内发现积水，可能影响设备安全',
                            impact: '该区间限速运行，运能下降20%',
                            measures: '排水处理，设备检查，恢复正常运行',
                            status: '已处理',
                            handler: '王工程师',
                            handleTime: '2023-11-20 19:30:00'
                        }
                    ];

                    // 风险预警数据
                    this.riskWarnings = [
                        { type: '设备老化', level: '中', count: 3, trend: 'stable', description: '部分设备接近维护周期' },
                        { type: '人员疲劳', level: '低', count: 1, trend: 'down', description: '夜班司机疲劳度监测' },
                        { type: '天气影响', level: '中', count: 2, trend: 'up', description: '近期雨雪天气增多' },
                        { type: '客流高峰', level: '高', count: 1, trend: 'up', description: '节假日客流压力大' }
                    ];

                    // 设置各项评分
                    this.equipmentSafetyScore = 94;
                    this.operationSafetyScore = 96;
                    this.personnelSafetyScore = 92;
                },
                calculateSafetyScore() {
                    // 增强的安全评分算法
                    const weights = {
                        equipment: 0.3,  // 设备安全权重30%
                        operation: 0.35, // 运行安全权重35%
                        personnel: 0.25, // 人员安全权重25%
                        environment: 0.1 // 环境安全权重10%
                    };

                    // 计算设备安全评分
                    this.equipmentSafetyScore = this.calculateEquipmentScore();

                    // 计算运行安全评分
                    this.operationSafetyScore = this.calculateOperationScore();

                    // 计算人员安全评分
                    this.personnelSafetyScore = this.calculatePersonnelScore();

                    // 环境安全评分（基于天气、客流等因素）
                    const environmentScore = this.calculateEnvironmentScore();

                    // 计算综合评分
                    let baseScore =
                        this.equipmentSafetyScore * weights.equipment +
                        this.operationSafetyScore * weights.operation +
                        this.personnelSafetyScore * weights.personnel +
                        environmentScore * weights.environment;

                    // 应用风险调整因子
                    const riskAdjustment = this.calculateRiskAdjustment();

                    // 应用历史事件影响因子
                    const historyAdjustment = this.calculateHistoryAdjustment();

                    this.safetyScore = Math.max(0, Math.min(100, Math.round(
                        baseScore * riskAdjustment * historyAdjustment
                    )));
                },
                calculateEquipmentScore() {
                    // 基于设备检查数据计算设备安全评分
                    let totalScore = 0;
                    let totalWeight = 0;

                    this.equipmentSafetyData.forEach(item => {
                        const weight = item.checkItems; // 检查项数量作为权重
                        totalScore += item.score * weight;
                        totalWeight += weight;
                    });

                    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
                },
                calculateOperationScore() {
                    // 基于运行数据计算运行安全评分
                    let totalScore = 0;
                    let count = 0;

                    this.operationSafetyData.forEach(item => {
                        // 根据偏差率调整评分
                        let adjustedScore = item.score;
                        if (Math.abs(item.deviation) > 10) {
                            adjustedScore *= 0.9; // 偏差大于10%时降低评分
                        } else if (Math.abs(item.deviation) > 5) {
                            adjustedScore *= 0.95; // 偏差大于5%时轻微降低评分
                        }

                        totalScore += adjustedScore;
                        count++;
                    });

                    return count > 0 ? Math.round(totalScore / count) : 0;
                },
                calculatePersonnelScore() {
                    // 基于人员安全数据计算人员安全评分
                    let totalScore = 0;
                    let count = 0;

                    this.personnelSafetyData.forEach(item => {
                        // 根据完成情况调整评分
                        let adjustedScore = item.score * (item.completion / 100);
                        totalScore += adjustedScore;
                        count++;
                    });

                    return count > 0 ? Math.round(totalScore / count) : 0;
                },
                calculateEnvironmentScore() {
                    // 计算环境安全评分（基于天气、客流、时间等因素）
                    let baseScore = 95;

                    // 天气因素调整
                    const weatherCondition = this.getCurrentWeatherCondition();
                    if (weatherCondition === 'severe') {
                        baseScore -= 10;
                    } else if (weatherCondition === 'moderate') {
                        baseScore -= 5;
                    }

                    // 客流因素调整
                    const passengerLoad = this.getCurrentPassengerLoad();
                    if (passengerLoad > 90) {
                        baseScore -= 8;
                    } else if (passengerLoad > 80) {
                        baseScore -= 4;
                    }

                    // 时间因素调整（高峰期风险较高）
                    const isPeakHour = this.isPeakHour();
                    if (isPeakHour) {
                        baseScore -= 3;
                    }

                    return Math.max(70, baseScore);
                },
                calculateRiskAdjustment() {
                    // 基于当前风险预警计算调整因子
                    let adjustment = 1.0;

                    this.riskWarnings.forEach(risk => {
                        if (risk.level === '高') {
                            adjustment *= 0.95; // 高风险降低5%
                        } else if (risk.level === '中') {
                            adjustment *= 0.98; // 中风险降低2%
                        }
                    });

                    return adjustment;
                },
                calculateHistoryAdjustment() {
                    // 基于近期安全事件计算调整因子
                    let adjustment = 1.0;
                    const recentIncidents = this.safetyIncidents.filter(incident => {
                        const incidentDate = new Date(incident.time);
                        const daysDiff = (new Date() - incidentDate) / (1000 * 60 * 60 * 24);
                        return daysDiff <= 7; // 近7天的事件
                    });

                    recentIncidents.forEach(incident => {
                        if (incident.level === '高') {
                            adjustment *= 0.92; // 高级事件降低8%
                        } else if (incident.level === '中') {
                            adjustment *= 0.96; // 中级事件降低4%
                        } else if (incident.level === '低') {
                            adjustment *= 0.98; // 低级事件降低2%
                        }
                    });

                    return adjustment;
                },
                getCurrentWeatherCondition() {
                    // 模拟获取当前天气状况
                    const conditions = ['good', 'moderate', 'severe'];
                    const weights = [0.7, 0.25, 0.05]; // 好天气概率70%，一般25%，恶劣5%
                    const random = Math.random();
                    let cumulative = 0;

                    for (let i = 0; i < conditions.length; i++) {
                        cumulative += weights[i];
                        if (random <= cumulative) {
                            return conditions[i];
                        }
                    }
                    return 'good';
                },
                getCurrentPassengerLoad() {
                    // 模拟获取当前客流负载率
                    return Math.floor(Math.random() * 40) + 60; // 60-100%之间
                },
                isPeakHour() {
                    // 判断是否为高峰时段
                    const hour = new Date().getHours();
                    return (hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19);
                },
                startRiskMonitoring() {
                    // 启动风险实时监测
                    this.riskMonitoringInterval = setInterval(() => {
                        this.performRiskAssessment();
                    }, 30000); // 每30秒检查一次
                },
                stopRiskMonitoring() {
                    // 停止风险监测
                    if (this.riskMonitoringInterval) {
                        clearInterval(this.riskMonitoringInterval);
                        this.riskMonitoringInterval = null;
                    }
                },
                performRiskAssessment() {
                    // 执行风险评估
                    const currentRisks = this.identifyCurrentRisks();

                    // 检查是否有新的高风险
                    currentRisks.forEach(risk => {
                        if (risk.level === '高' && !this.isRiskAlreadyWarned(risk)) {
                            this.triggerRiskAlert(risk);
                        }
                    });

                    // 更新风险预警数据
                    this.updateRiskWarnings(currentRisks);
                },
                identifyCurrentRisks() {
                    // 识别当前风险
                    const risks = [];

                    // 检查设备风险
                    const equipmentRisks = this.assessEquipmentRisks();
                    risks.push(...equipmentRisks);

                    // 检查运行风险
                    const operationRisks = this.assessOperationRisks();
                    risks.push(...operationRisks);

                    // 检查人员风险
                    const personnelRisks = this.assessPersonnelRisks();
                    risks.push(...personnelRisks);

                    // 检查环境风险
                    const environmentRisks = this.assessEnvironmentRisks();
                    risks.push(...environmentRisks);

                    return risks;
                },
                assessEquipmentRisks() {
                    // 评估设备风险
                    const risks = [];

                    this.equipmentSafetyData.forEach(equipment => {
                        if (equipment.passRate < 90) {
                            risks.push({
                                type: '设备故障风险',
                                level: equipment.passRate < 80 ? '高' : '中',
                                source: equipment.category,
                                description: `${equipment.category}通过率仅为${equipment.passRate}%`,
                                suggestion: '建议立即进行设备检修和维护',
                                timestamp: new Date().toISOString()
                            });
                        }
                    });

                    return risks;
                },
                assessOperationRisks() {
                    // 评估运行风险
                    const risks = [];

                    this.operationSafetyData.forEach(operation => {
                        if (Math.abs(operation.deviation) > 15) {
                            risks.push({
                                type: '运行异常风险',
                                level: Math.abs(operation.deviation) > 25 ? '高' : '中',
                                source: operation.category,
                                description: `${operation.category}偏差率达到${operation.deviation}%`,
                                suggestion: operation.suggestion || '建议检查运行参数并调整',
                                timestamp: new Date().toISOString()
                            });
                        }
                    });

                    return risks;
                },
                assessPersonnelRisks() {
                    // 评估人员风险
                    const risks = [];

                    this.personnelSafetyData.forEach(personnel => {
                        if (personnel.completion < 80) {
                            risks.push({
                                type: '人员安全风险',
                                level: personnel.completion < 60 ? '高' : '中',
                                source: personnel.category,
                                description: `${personnel.category}完成率仅为${personnel.completion}%`,
                                suggestion: '建议加强人员培训和管理',
                                timestamp: new Date().toISOString()
                            });
                        }
                    });

                    return risks;
                },
                assessEnvironmentRisks() {
                    // 评估环境风险
                    const risks = [];

                    // 检查天气风险
                    const weather = this.getCurrentWeatherCondition();
                    if (weather === 'severe') {
                        risks.push({
                            type: '恶劣天气风险',
                            level: '高',
                            source: '天气监测',
                            description: '当前天气条件恶劣，可能影响列车安全运行',
                            suggestion: '建议降低运行速度，增加安全检查频次',
                            timestamp: new Date().toISOString()
                        });
                    }

                    // 检查客流风险
                    const passengerLoad = this.getCurrentPassengerLoad();
                    if (passengerLoad > 95) {
                        risks.push({
                            type: '客流过载风险',
                            level: '中',
                            source: '客流监测',
                            description: `当前客流负载率达到${passengerLoad}%`,
                            suggestion: '建议增加列车班次，疏导客流',
                            timestamp: new Date().toISOString()
                        });
                    }

                    return risks;
                },
                isRiskAlreadyWarned(risk) {
                    // 检查风险是否已经预警过
                    return this.warnedRisks && this.warnedRisks.some(warned =>
                        warned.type === risk.type &&
                        warned.source === risk.source &&
                        (new Date() - new Date(warned.timestamp)) < 300000 // 5分钟内不重复预警
                    );
                },
                triggerRiskAlert(risk) {
                    // 触发风险预警
                    this.$notify({
                        title: '安全风险预警',
                        message: `${risk.type}: ${risk.description}`,
                        type: 'warning',
                        duration: 0, // 不自动关闭
                        position: 'top-right'
                    });

                    // 记录已预警的风险
                    if (!this.warnedRisks) {
                        this.warnedRisks = [];
                    }
                    this.warnedRisks.push(risk);

                    // 可以在这里添加其他预警机制，如发送短信、邮件等
                    console.log('风险预警触发:', risk);
                },
                updateRiskWarnings(currentRisks) {
                    // 更新风险预警显示
                    const riskSummary = this.summarizeRisks(currentRisks);
                    this.riskWarnings = riskSummary;
                },
                summarizeRisks(risks) {
                    // 汇总风险信息
                    const summary = {};

                    risks.forEach(risk => {
                        const key = risk.type;
                        if (!summary[key]) {
                            summary[key] = {
                                type: key,
                                level: risk.level,
                                count: 0,
                                trend: 'stable',
                                description: risk.description
                            };
                        }
                        summary[key].count++;

                        // 取最高风险等级
                        if (risk.level === '高' || (risk.level === '中' && summary[key].level === '低')) {
                            summary[key].level = risk.level;
                        }
                    });

                    return Object.values(summary);
                },
                calculateIncidentStatistics() {
                    // 计算安全事件统计数据
                    const stats = {
                        totalIncidents: this.safetyIncidents.length,
                        resolvedIncidents: 0,
                        pendingIncidents: 0,
                        avgResolutionTime: 0,
                        incidentsByType: {},
                        incidentsByLevel: {},
                        monthlyTrend: []
                    };

                    let totalResolutionTime = 0;
                    let resolvedCount = 0;

                    this.safetyIncidents.forEach(incident => {
                        // 统计处理状态
                        if (incident.status === '已处理' || incident.status === '已关闭') {
                            stats.resolvedIncidents++;

                            // 计算处理时间
                            if (incident.handleTime && incident.time) {
                                const startTime = new Date(incident.time);
                                const endTime = new Date(incident.handleTime);
                                const resolutionTime = (endTime - startTime) / (1000 * 60 * 60); // 小时
                                totalResolutionTime += resolutionTime;
                                resolvedCount++;
                            }
                        } else {
                            stats.pendingIncidents++;
                        }

                        // 按类型统计
                        if (!stats.incidentsByType[incident.type]) {
                            stats.incidentsByType[incident.type] = 0;
                        }
                        stats.incidentsByType[incident.type]++;

                        // 按级别统计
                        if (!stats.incidentsByLevel[incident.level]) {
                            stats.incidentsByLevel[incident.level] = 0;
                        }
                        stats.incidentsByLevel[incident.level]++;
                    });

                    // 计算平均处理时间
                    stats.avgResolutionTime = resolvedCount > 0 ?
                        Math.round(totalResolutionTime / resolvedCount * 10) / 10 : 0;

                    // 生成月度趋势数据
                    stats.monthlyTrend = this.generateMonthlyTrend();

                    this.incidentStatistics = stats;
                },
                generateMonthlyTrend() {
                    // 生成近6个月的事件趋势数据
                    const months = [];
                    const now = new Date();

                    for (let i = 5; i >= 0; i--) {
                        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
                        const monthName = date.toLocaleDateString('zh-CN', { month: 'short' });

                        // 模拟月度事件数量（实际应用中从数据库查询）
                        const count = Math.floor(Math.random() * 10) + 2;

                        months.push({
                            month: monthName,
                            count: count,
                            resolved: Math.floor(count * 0.8),
                            pending: Math.floor(count * 0.2)
                        });
                    }

                    return months;
                },
                analyzeIncidentTrends() {
                    // 分析事件趋势
                    const analysis = {
                        riskLevel: 'low',
                        trendDirection: 'stable',
                        recommendations: []
                    };

                    // 分析事件数量趋势
                    const recentMonths = this.incidentStatistics.monthlyTrend.slice(-3);
                    const avgRecent = recentMonths.reduce((sum, month) => sum + month.count, 0) / 3;
                    const previousMonths = this.incidentStatistics.monthlyTrend.slice(-6, -3);
                    const avgPrevious = previousMonths.reduce((sum, month) => sum + month.count, 0) / 3;

                    if (avgRecent > avgPrevious * 1.2) {
                        analysis.trendDirection = 'increasing';
                        analysis.riskLevel = 'high';
                        analysis.recommendations.push('事件数量呈上升趋势，建议加强安全管理');
                    } else if (avgRecent < avgPrevious * 0.8) {
                        analysis.trendDirection = 'decreasing';
                        analysis.recommendations.push('事件数量呈下降趋势，安全管理效果良好');
                    }

                    // 分析处理效率
                    const resolutionRate = this.incidentStatistics.resolvedIncidents / this.incidentStatistics.totalIncidents;
                    if (resolutionRate < 0.8) {
                        analysis.riskLevel = 'medium';
                        analysis.recommendations.push('事件处理效率偏低，建议优化处理流程');
                    }

                    // 分析平均处理时间
                    if (this.incidentStatistics.avgResolutionTime > 24) {
                        analysis.recommendations.push('平均处理时间较长，建议提高响应速度');
                    }

                    return analysis;
                },
                generateIncidentReport() {
                    // 生成安全事件报告
                    const analysis = this.analyzeIncidentTrends();
                    const report = {
                        reportDate: new Date().toLocaleDateString('zh-CN'),
                        statistics: this.incidentStatistics,
                        analysis: analysis,
                        summary: this.generateReportSummary()
                    };

                    // 这里可以调用后端API保存报告或导出文件
                    console.log('安全事件报告:', report);

                    this.$message({
                        message: '安全事件报告已生成',
                        type: 'success'
                    });

                    return report;
                },
                generateReportSummary() {
                    // 生成报告摘要
                    const stats = this.incidentStatistics;
                    const resolutionRate = Math.round(stats.resolvedIncidents / stats.totalIncidents * 100);

                    return `本期共发生安全事件${stats.totalIncidents}起，已处理${stats.resolvedIncidents}起，处理率${resolutionRate}%。平均处理时间${stats.avgResolutionTime}小时。主要事件类型为${Object.keys(stats.incidentsByType)[0]}。`;
                },
                refreshSafetyData() {
                    this.loadSafetyData();
                    this.$message({
                        message: '安全评估数据已刷新',
                        type: 'success'
                    });
                },
                getScoreColor() {
                    if (this.safetyScore >= 95) return '#67C23A';
                    if (this.safetyScore >= 85) return '#E6A23C';
                    return '#F56C6C';
                },
                getScoreDesc() {
                    if (this.safetyScore >= 95) return '安全状况优秀';
                    if (this.safetyScore >= 90) return '安全状况良好';
                    if (this.safetyScore >= 85) return '安全状况一般';
                    if (this.safetyScore >= 80) return '存在安全隐患';
                    return '安全状况较差';
                },
                getIndicatorClass(score) {
                    if (score >= 95) return 'indicator-excellent';
                    if (score >= 90) return 'indicator-good';
                    if (score >= 85) return 'indicator-normal';
                    if (score >= 80) return 'indicator-warning';
                    return 'indicator-danger';
                },
                getIndicatorColor(score) {
                    if (score >= 95) return '#67C23A';
                    if (score >= 90) return '#95D475';
                    if (score >= 85) return '#E6A23C';
                    if (score >= 80) return '#F78989';
                    return '#F56C6C';
                },
                getStatusType(status) {
                    const typeMap = {
                        '优秀': 'success',
                        '正常': 'success',
                        '注意': 'warning',
                        '异常': 'danger'
                    };
                    return typeMap[status] || 'info';
                },
                getDeviationClass(deviation) {
                    if (Math.abs(deviation) <= 5) return 'deviation-normal';
                    if (Math.abs(deviation) <= 10) return 'deviation-warning';
                    return 'deviation-danger';
                },
                getIncidentLevelType(level) {
                    const typeMap = {
                        '高': 'danger',
                        '中': 'warning',
                        '低': 'info'
                    };
                    return typeMap[level] || 'info';
                },
                addSafetyIncident() {
                    this.incidentDialogTitle = '添加安全事件';
                    this.incidentForm = {
                        id: 'SI' + new Date().getTime(),
                        time: '',
                        type: '',
                        level: '',
                        location: '',
                        description: '',
                        impact: '',
                        measures: '',
                        status: '待处理',
                        responsible: '',
                        recorder: '当前用户'
                    };
                    this.incidentFormDialogVisible = true;
                },
                submitIncidentForm() {
                    this.$refs.incidentForm.validate((valid) => {
                        if (valid) {
                            const formData = {
                                ...this.incidentForm,
                                handler: '当前用户',
                                handleTime: new Date().toLocaleString(),
                                reportTime: new Date().toLocaleString()
                            };

                            // 查找是否已存在该ID的事件
                            const index = this.safetyIncidents.findIndex(item => item.id === formData.id);

                            if (index !== -1) {
                                // 更新现有事件
                                this.$set(this.safetyIncidents, index, formData);
                                this.$message({
                                    message: '安全事件已更新',
                                    type: 'success'
                                });
                            } else {
                                // 添加新事件
                                this.safetyIncidents.unshift(formData);
                                this.$message({
                                    message: '安全事件已添加',
                                    type: 'success'
                                });
                            }

                            this.incidentFormDialogVisible = false;
                        }
                    });
                },
                viewIncidentDetail(incident) {
                    this.$alert(
                        `<div style="text-align: left;">
                            <p><strong>事件ID：</strong>${incident.id}</p>
                            <p><strong>发生时间：</strong>${incident.time}</p>
                            <p><strong>事件类型：</strong>${incident.type}</p>
                            <p><strong>事件级别：</strong>${incident.level}</p>
                            <p><strong>发生位置：</strong>${incident.location}</p>
                            <p><strong>事件描述：</strong>${incident.description}</p>
                            <p><strong>影响范围：</strong>${incident.impact}</p>
                            <p><strong>处理措施：</strong>${incident.measures}</p>
                            <p><strong>处理状态：</strong>${incident.status}</p>
                            <p><strong>处理人员：</strong>${incident.handler}</p>
                            <p><strong>处理时间：</strong>${incident.handleTime}</p>
                        </div>`,
                        '安全事件详情',
                        {
                            dangerouslyUseHTMLString: true,
                            confirmButtonText: '确定'
                        }
                    );
                },
                editIncident(incident) {
                    this.incidentDialogTitle = '编辑安全事件';
                    this.incidentForm = JSON.parse(JSON.stringify(incident));
                    this.incidentDialogVisible = false;
                    this.incidentFormDialogVisible = true;
                },
                getIncidentStatusType(status) {
                    const typeMap = {
                        '待处理': 'warning',
                        '处理中': 'primary',
                        '已处理': 'success',
                        '已关闭': 'info'
                    };
                    return typeMap[status] || 'info';
                },
                getIncidentStepActive() {
                    if (!this.currentIncident) return 0;
                    const statusMap = {
                        '待处理': 1,
                        '处理中': 2,
                        '已处理': 3,
                        '已关闭': 4
                    };
                    return statusMap[this.currentIncident.status] || 0;
                },
                handleSizeChange(val) {
                    console.log(`每页 ${val} 条`);
                },
                handleCurrentChange(val) {
                    console.log(`当前页: ${val}`);
                },
                getRiskLevelType(level) {
                    const typeMap = {
                        '高': 'danger',
                        '中': 'warning',
                        '低': 'info'
                    };
                    return typeMap[level] || 'info';
                },
                getRiskTrendIcon(trend) {
                    const iconMap = {
                        'up': 'el-icon-top',
                        'down': 'el-icon-bottom',
                        'stable': 'el-icon-minus'
                    };
                    return iconMap[trend] || 'el-icon-minus';
                },
                getRiskTrendColor(trend) {
                    const colorMap = {
                        'up': '#F56C6C',
                        'down': '#67C23A',
                        'stable': '#909399'
                    };
                    return colorMap[trend] || '#909399';
                },
                getRiskTrendText(trend) {
                    const textMap = {
                        'up': '上升',
                        'down': '下降',
                        'stable': '稳定'
                    };
                    return textMap[trend] || '稳定';
                },
                exportSafetyReport() {
                    // 生成安全评估报告
                    const report = this.generateSafetyReport();

                    // 创建下载链接
                    const dataStr = JSON.stringify(report, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);

                    // 创建下载元素
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `安全评估报告_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.json`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // 释放URL对象
                    URL.revokeObjectURL(url);

                    this.$message({
                        message: '安全评估报告已导出',
                        type: 'success'
                    });
                },
                generateSafetyReport() {
                    // 生成完整的安全评估报告
                    return {
                        reportInfo: {
                            title: '轨道交通运输列车运行安全评估报告',
                            generateTime: new Date().toLocaleString('zh-CN'),
                            period: this.selectedPeriod,
                            version: '1.0'
                        },
                        safetyScore: {
                            overall: this.safetyScore,
                            equipment: this.equipmentSafetyScore,
                            operation: this.operationSafetyScore,
                            personnel: this.personnelSafetyScore,
                            indicators: this.safetyIndicators
                        },
                        riskAssessment: {
                            currentRisks: this.riskWarnings,
                            riskAnalysis: this.analyzeIncidentTrends()
                        },
                        incidentAnalysis: {
                            statistics: this.incidentStatistics,
                            incidents: this.safetyIncidents,
                            summary: this.generateReportSummary()
                        },
                        recommendations: this.generateRecommendations(),
                        appendix: {
                            equipmentData: this.equipmentSafetyData,
                            operationData: this.operationSafetyData,
                            personnelData: this.personnelSafetyData
                        }
                    };
                },
                generateRecommendations() {
                    // 生成改进建议
                    const recommendations = [];

                    // 基于安全评分生成建议
                    if (this.safetyScore < 90) {
                        recommendations.push({
                            category: '综合安全',
                            priority: 'high',
                            description: '综合安全评分偏低，建议全面检查安全管理体系',
                            actions: ['加强安全培训', '完善安全制度', '增加安全检查频次']
                        });
                    }

                    // 基于设备安全生成建议
                    if (this.equipmentSafetyScore < 95) {
                        recommendations.push({
                            category: '设备安全',
                            priority: 'medium',
                            description: '设备安全评分有待提升',
                            actions: ['加强设备维护', '更新老旧设备', '完善设备监控系统']
                        });
                    }

                    // 基于风险预警生成建议
                    this.riskWarnings.forEach(risk => {
                        if (risk.level === '高') {
                            recommendations.push({
                                category: '风险管控',
                                priority: 'high',
                                description: `${risk.type}风险等级较高`,
                                actions: ['立即制定应对措施', '加强监控', '准备应急预案']
                            });
                        }
                    });

                    return recommendations;
                },
                initSafetyCharts() {
                    // 初始化安全趋势图表
                    this.initSafetyTrendChart();

                    // 初始化事件类型分布图表
                    this.initIncidentTypeChart();

                    // 初始化风险等级分布图表
                    this.initRiskLevelChart();

                    // 初始化月度事件趋势图表
                    this.initMonthlyIncidentChart();
                },
                initSafetyTrendChart() {
                    if (document.getElementById('safety-trend-chart')) {
                        const trendChart = echarts.init(document.getElementById('safety-trend-chart'));
                        trendChart.setOption({
                            tooltip: {
                                trigger: 'axis'
                            },
                            legend: {
                                data: ['综合评分', '设备安全', '运行安全', '人员安全'],
                                top: 'bottom'
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: ['11-16', '11-17', '11-18', '11-19', '11-20', '11-21', '11-22']
                            },
                            yAxis: {
                                type: 'value',
                                min: 80,
                                max: 100
                            },
                            series: [
                                {
                                    name: '综合评分',
                                    type: 'line',
                                    data: [92, 94, 93, 95, 94, 96, 95],
                                    itemStyle: { color: '#409EFF' },
                                    smooth: true
                                },
                                {
                                    name: '设备安全',
                                    type: 'line',
                                    data: [91, 93, 92, 94, 93, 95, 94],
                                    itemStyle: { color: '#67C23A' },
                                    smooth: true
                                },
                                {
                                    name: '运行安全',
                                    type: 'line',
                                    data: [94, 96, 95, 97, 96, 98, 96],
                                    itemStyle: { color: '#E6A23C' },
                                    smooth: true
                                },
                                {
                                    name: '人员安全',
                                    type: 'line',
                                    data: [90, 92, 91, 93, 92, 94, 92],
                                    itemStyle: { color: '#F56C6C' },
                                    smooth: true
                                }
                            ]
                        });

                        this.safetyCharts = this.safetyCharts || [];
                        this.safetyCharts.push(trendChart);
                    }
                },
                initIncidentTypeChart() {
                    if (document.getElementById('incident-type-chart')) {
                        const typeChart = echarts.init(document.getElementById('incident-type-chart'));
                        const typeData = Object.entries(this.incidentStatistics.incidentsByType).map(([type, count]) => ({
                            name: type,
                            value: count
                        }));

                        typeChart.setOption({
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b}: {c} ({d}%)'
                            },
                            legend: {
                                orient: 'vertical',
                                left: 'left',
                                data: typeData.map(item => item.name)
                            },
                            series: [
                                {
                                    name: '事件类型',
                                    type: 'pie',
                                    radius: '60%',
                                    center: ['60%', '50%'],
                                    data: typeData,
                                    emphasis: {
                                        itemStyle: {
                                            shadowBlur: 10,
                                            shadowOffsetX: 0,
                                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                                        }
                                    }
                                }
                            ]
                        });

                        this.safetyCharts = this.safetyCharts || [];
                        this.safetyCharts.push(typeChart);
                    }
                },
                initRiskLevelChart() {
                    if (document.getElementById('risk-level-chart')) {
                        const riskChart = echarts.init(document.getElementById('risk-level-chart'));

                        riskChart.setOption({
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: ['设备风险', '运行风险', '人员风险', '环境风险']
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: [
                                {
                                    name: '高风险',
                                    type: 'bar',
                                    stack: 'total',
                                    data: [1, 0, 0, 1],
                                    itemStyle: { color: '#F56C6C' }
                                },
                                {
                                    name: '中风险',
                                    type: 'bar',
                                    stack: 'total',
                                    data: [2, 1, 1, 1],
                                    itemStyle: { color: '#E6A23C' }
                                },
                                {
                                    name: '低风险',
                                    type: 'bar',
                                    stack: 'total',
                                    data: [1, 2, 1, 0],
                                    itemStyle: { color: '#409EFF' }
                                }
                            ]
                        });

                        this.safetyCharts = this.safetyCharts || [];
                        this.safetyCharts.push(riskChart);
                    }
                },
                initMonthlyIncidentChart() {
                    if (document.getElementById('monthly-incident-chart')) {
                        const monthlyChart = echarts.init(document.getElementById('monthly-incident-chart'));
                        const monthlyData = this.incidentStatistics.monthlyTrend;

                        monthlyChart.setOption({
                            tooltip: {
                                trigger: 'axis'
                            },
                            legend: {
                                data: ['总事件', '已处理', '待处理'],
                                top: 'bottom'
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: monthlyData.map(item => item.month)
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: [
                                {
                                    name: '总事件',
                                    type: 'line',
                                    data: monthlyData.map(item => item.count),
                                    itemStyle: { color: '#409EFF' }
                                },
                                {
                                    name: '已处理',
                                    type: 'bar',
                                    data: monthlyData.map(item => item.resolved),
                                    itemStyle: { color: '#67C23A' }
                                },
                                {
                                    name: '待处理',
                                    type: 'bar',
                                    data: monthlyData.map(item => item.pending),
                                    itemStyle: { color: '#F56C6C' }
                                }
                            ]
                        });

                        this.safetyCharts = this.safetyCharts || [];
                        this.safetyCharts.push(monthlyChart);
                    }
                },
                resizeCharts() {
                    // 调整所有图表大小
                    if (this.safetyCharts) {
                        this.safetyCharts.forEach(chart => {
                            if (chart && chart.resize) {
                                chart.resize();
                            }
                        });
                    }
                }
            },
            watch: {
                selectedPeriod(newPeriod) {
                    this.loadSafetyData();
                }
            }
        }
    },
    // 添加运营数据统计路由
    {
        path: '/statistics',
        component: {
            template: '#statistics-template',
            data() {
                return {
                    loading: true,
                    timeRange: 'month',
                    activeTab: 'passenger',
                    coreMetrics: [],
                    passengerData: {
                        dailyTrend: [],
                        lineDistribution: [],
                        peakHours: [],
                        heatmapData: []
                    },
                    efficiencyData: [],
                    revenueData: {
                        total: 0,
                        totalChange: 0,
                        avgTicketPrice: 0,
                        priceChange: 0,
                        avgRevPerPassenger: 0,
                        revenuePerPassengerChange: 0,
                        monthlyTrend: [],
                        lineRevenue: []
                    },
                    equipmentData: [],
                    statisticsCharts: []
                };
            },
            mounted() {
                this.loadStatisticsData();
                // 监听窗口大小变化
                window.addEventListener('resize', this.resizeCharts);
            },
            beforeDestroy() {
                // 移除窗口大小变化监听器
                window.removeEventListener('resize', this.resizeCharts);
            },
            methods: {
                loadStatisticsData() {
                    this.loading = true;
                    // 模拟数据加载
                    setTimeout(() => {
                        this.loading = false;
                        this.initCoreMetrics();
                        this.initPassengerData();
                        this.initEfficiencyData();
                        this.initRevenueData();
                        this.initEquipmentData();
                        this.initStatisticsCharts();
                    }, 1000);
                },
                initCoreMetrics() {
                    // 初始化核心指标数据
                    this.coreMetrics = [
                        {
                            type: 'passenger',
                            icon: 'el-icon-user',
                            label: '日客流量',
                            value: '1,285,000',
                            change: '+5.2%',
                            trend: 'positive'
                        },
                        {
                            type: 'revenue',
                            icon: 'el-icon-money',
                            label: '日营收',
                            value: '¥2,856,000',
                            change: '+3.8%',
                            trend: 'positive'
                        },
                        {
                            type: 'efficiency',
                            icon: 'el-icon-time',
                            label: '准点率',
                            value: '98.6%',
                            change: '+0.4%',
                            trend: 'positive'
                        },
                        {
                            type: 'utilization',
                            icon: 'el-icon-setting',
                            label: '设备利用率',
                            value: '94.2%',
                            change: '-1.2%',
                            trend: 'negative'
                        }
                    ];
                },
                initPassengerData() {
                    // 初始化客流数据
                    this.passengerData = {
                        dailyTrend: [
                            { date: '11-16', count: 1250000, workday: true },
                            { date: '11-17', count: 1320000, workday: true },
                            { date: '11-18', count: 980000, workday: false },
                            { date: '11-19', count: 850000, workday: false },
                            { date: '11-20', count: 1380000, workday: true },
                            { date: '11-21', count: 1420000, workday: true },
                            { date: '11-22', count: 1285000, workday: true }
                        ],
                        lineDistribution: [
                            { line: '1号线', count: 285000, percentage: 22.2 },
                            { line: '2号线', count: 320000, percentage: 24.9 },
                            { line: '4号线', count: 195000, percentage: 15.2 },
                            { line: '5号线', count: 245000, percentage: 19.1 },
                            { line: '6号线', count: 240000, percentage: 18.7 }
                        ],
                        peakHours: [
                            { hour: '6:00', count: 45000 },
                            { hour: '7:00', count: 125000 },
                            { hour: '8:00', count: 185000 },
                            { hour: '9:00', count: 142000 },
                            { hour: '10:00', count: 98000 },
                            { hour: '11:00', count: 85000 },
                            { hour: '12:00', count: 110000 },
                            { hour: '17:00', count: 158000 },
                            { hour: '18:00', count: 175000 },
                            { hour: '19:00', count: 132000 },
                            { hour: '20:00', count: 95000 },
                            { hour: '21:00', count: 68000 }
                        ]
                    };
                },
                initEfficiencyData() {
                    // 初始化运行效率数据
                    this.efficiencyData = [
                        {
                            line: '1号线',
                            punctuality: 98.5,
                            loadFactor: 85.3,
                            avgSpeed: 42.5,
                            efficiency: 92.5,
                            dailyTrips: 286,
                            totalDistance: 1250,
                            energyConsumption: 15800
                        },
                        {
                            line: '2号线',
                            punctuality: 97.2,
                            loadFactor: 87.2,
                            avgSpeed: 38.9,
                            efficiency: 91.8,
                            dailyTrips: 312,
                            totalDistance: 1180,
                            energyConsumption: 16200
                        },
                        {
                            line: '4号线',
                            punctuality: 98.9,
                            loadFactor: 82.5,
                            avgSpeed: 45.2,
                            efficiency: 93.2,
                            dailyTrips: 198,
                            totalDistance: 980,
                            energyConsumption: 12500
                        },
                        {
                            line: '5号线',
                            punctuality: 99.1,
                            loadFactor: 80.7,
                            avgSpeed: 46.8,
                            efficiency: 94.5,
                            dailyTrips: 245,
                            totalDistance: 1120,
                            energyConsumption: 14200
                        },
                        {
                            line: '6号线',
                            punctuality: 96.5,
                            loadFactor: 89.1,
                            avgSpeed: 41.2,
                            efficiency: 90.7,
                            dailyTrips: 268,
                            totalDistance: 1080,
                            energyConsumption: 15600
                        }
                    ];
                },
                initRevenueData() {
                    // 初始化收入数据
                    this.revenueData = {
                        total: 2856000,
                        totalChange: 3.8,
                        avgTicketPrice: 4.2,
                        priceChange: -0.5,
                        avgRevPerPassenger: 2.22,
                        revenuePerPassengerChange: 1.2,
                        monthlyTrend: [
                            { month: '7月', revenue: 78500000, passengers: 35200000 },
                            { month: '8月', revenue: 82300000, passengers: 36800000 },
                            { month: '9月', revenue: 79800000, passengers: 35900000 },
                            { month: '10月', revenue: 85600000, passengers: 38500000 },
                            { month: '11月', revenue: 88200000, passengers: 39800000 }
                        ],
                        lineRevenue: [
                            { line: '1号线', revenue: 634200, percentage: 22.2 },
                            { line: '2号线', revenue: 710400, percentage: 24.9 },
                            { line: '4号线', revenue: 433800, percentage: 15.2 },
                            { line: '5号线', revenue: 544680, percentage: 19.1 },
                            { line: '6号线', revenue: 532920, percentage: 18.7 }
                        ]
                    };
                },
                initEquipmentData() {
                    // 初始化设备数据
                    this.equipmentData = [
                        {
                            equipment: '列车T1208',
                            type: '动车组',
                            utilization: 94.5,
                            status: '运行中',
                            lastMaintenance: '2023-10-15',
                            nextMaintenance: '2023-12-15',
                            maintenanceCost: '¥25,000'
                        },
                        {
                            equipment: '信号系统A1',
                            type: '信号设备',
                            utilization: 98.2,
                            status: '正常',
                            lastMaintenance: '2023-11-01',
                            nextMaintenance: '2024-01-01',
                            maintenanceCost: '¥8,000'
                        },
                        {
                            equipment: '供电系统B2',
                            type: '供电设备',
                            utilization: 92.8,
                            status: '正常',
                            lastMaintenance: '2023-09-20',
                            nextMaintenance: '2023-12-20',
                            maintenanceCost: '¥15,000'
                        },
                        {
                            equipment: '通信系统C3',
                            type: '通信设备',
                            utilization: 89.6,
                            status: '维护中',
                            lastMaintenance: '2023-11-18',
                            nextMaintenance: '2024-02-18',
                            maintenanceCost: '¥12,000'
                        }
                    ];
                },
                // 高级数据分析功能
                performDataAnalysis() {
                    // 执行综合数据分析
                    const analysis = {
                        trendAnalysis: this.analyzeTrends(),
                        anomalyDetection: this.detectAnomalies(),
                        correlationAnalysis: this.analyzeCorrelations(),
                        prediction: this.predictTrends(),
                        recommendations: this.generateRecommendations()
                    };

                    return analysis;
                },
                analyzeTrends() {
                    // 趋势分析
                    const passengerTrend = this.calculateTrend(this.passengerData.dailyTrend.map(item => item.count));
                    const revenueTrend = this.calculateTrend(this.revenueData.monthlyTrend.map(item => item.revenue));

                    return {
                        passenger: {
                            direction: passengerTrend > 0 ? 'increasing' : passengerTrend < 0 ? 'decreasing' : 'stable',
                            rate: Math.abs(passengerTrend),
                            confidence: 0.85
                        },
                        revenue: {
                            direction: revenueTrend > 0 ? 'increasing' : revenueTrend < 0 ? 'decreasing' : 'stable',
                            rate: Math.abs(revenueTrend),
                            confidence: 0.92
                        }
                    };
                },
                calculateTrend(data) {
                    // 计算线性趋势
                    const n = data.length;
                    const x = Array.from({length: n}, (_, i) => i);
                    const sumX = x.reduce((a, b) => a + b, 0);
                    const sumY = data.reduce((a, b) => a + b, 0);
                    const sumXY = x.reduce((sum, xi, i) => sum + xi * data[i], 0);
                    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

                    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
                    return slope;
                },
                detectAnomalies() {
                    // 异常检测
                    const anomalies = [];

                    // 检测客流异常
                    const passengerMean = this.passengerData.dailyTrend.reduce((sum, item) => sum + item.count, 0) / this.passengerData.dailyTrend.length;
                    const passengerStd = Math.sqrt(
                        this.passengerData.dailyTrend.reduce((sum, item) => sum + Math.pow(item.count - passengerMean, 2), 0) / this.passengerData.dailyTrend.length
                    );

                    this.passengerData.dailyTrend.forEach(item => {
                        const zScore = Math.abs(item.count - passengerMean) / passengerStd;
                        if (zScore > 2) {
                            anomalies.push({
                                type: 'passenger',
                                date: item.date,
                                value: item.count,
                                severity: zScore > 3 ? 'high' : 'medium',
                                description: `客流量异常: ${item.count.toLocaleString()}人次`
                            });
                        }
                    });

                    // 检测效率异常
                    this.efficiencyData.forEach(line => {
                        if (line.punctuality < 95) {
                            anomalies.push({
                                type: 'efficiency',
                                line: line.line,
                                value: line.punctuality,
                                severity: line.punctuality < 90 ? 'high' : 'medium',
                                description: `${line.line}准点率异常: ${line.punctuality}%`
                            });
                        }

                        if (line.efficiency < 85) {
                            anomalies.push({
                                type: 'efficiency',
                                line: line.line,
                                value: line.efficiency,
                                severity: line.efficiency < 80 ? 'high' : 'medium',
                                description: `${line.line}运行效率异常: ${line.efficiency}%`
                            });
                        }
                    });

                    return anomalies;
                },
                analyzeCorrelations() {
                    // 关联分析
                    const correlations = [];

                    // 分析客流与收入的关联
                    const passengerRevCorr = this.calculateCorrelation(
                        this.revenueData.monthlyTrend.map(item => item.passengers),
                        this.revenueData.monthlyTrend.map(item => item.revenue)
                    );

                    correlations.push({
                        variables: ['客流量', '营收'],
                        coefficient: passengerRevCorr,
                        strength: this.getCorrelationStrength(passengerRevCorr),
                        description: `客流量与营收的相关系数为${passengerRevCorr.toFixed(3)}`
                    });

                    // 分析准点率与满载率的关联
                    const punctualityLoadCorr = this.calculateCorrelation(
                        this.efficiencyData.map(item => item.punctuality),
                        this.efficiencyData.map(item => item.loadFactor)
                    );

                    correlations.push({
                        variables: ['准点率', '满载率'],
                        coefficient: punctualityLoadCorr,
                        strength: this.getCorrelationStrength(punctualityLoadCorr),
                        description: `准点率与满载率的相关系数为${punctualityLoadCorr.toFixed(3)}`
                    });

                    return correlations;
                },
                calculateCorrelation(x, y) {
                    // 计算皮尔逊相关系数
                    const n = x.length;
                    const sumX = x.reduce((a, b) => a + b, 0);
                    const sumY = y.reduce((a, b) => a + b, 0);
                    const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
                    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);
                    const sumYY = y.reduce((sum, yi) => sum + yi * yi, 0);

                    const numerator = n * sumXY - sumX * sumY;
                    const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));

                    return denominator === 0 ? 0 : numerator / denominator;
                },
                getCorrelationStrength(coefficient) {
                    const abs = Math.abs(coefficient);
                    if (abs >= 0.8) return 'strong';
                    if (abs >= 0.5) return 'moderate';
                    if (abs >= 0.3) return 'weak';
                    return 'very weak';
                },
                predictTrends() {
                    // 趋势预测
                    const predictions = {};

                    // 预测未来7天客流
                    const passengerData = this.passengerData.dailyTrend.map(item => item.count);
                    const passengerTrend = this.calculateTrend(passengerData);
                    const lastPassenger = passengerData[passengerData.length - 1];

                    predictions.passenger = [];
                    for (let i = 1; i <= 7; i++) {
                        const predicted = lastPassenger + passengerTrend * i;
                        predictions.passenger.push({
                            date: this.getDateAfter(i),
                            predicted: Math.max(0, Math.round(predicted)),
                            confidence: Math.max(0.5, 0.9 - i * 0.05)
                        });
                    }

                    // 预测下月收入
                    const revenueData = this.revenueData.monthlyTrend.map(item => item.revenue);
                    const revenueTrend = this.calculateTrend(revenueData);
                    const lastRevenue = revenueData[revenueData.length - 1];

                    predictions.revenue = {
                        nextMonth: Math.max(0, Math.round(lastRevenue + revenueTrend)),
                        confidence: 0.85,
                        range: {
                            min: Math.round((lastRevenue + revenueTrend) * 0.9),
                            max: Math.round((lastRevenue + revenueTrend) * 1.1)
                        }
                    };

                    return predictions;
                },
                getDateAfter(days) {
                    const date = new Date();
                    date.setDate(date.getDate() + days);
                    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
                },
                generateRecommendations() {
                    // 生成数据驱动的建议
                    const recommendations = [];
                    const analysis = this.performDataAnalysis();

                    // 基于趋势分析的建议
                    if (analysis.trendAnalysis.passenger.direction === 'decreasing') {
                        recommendations.push({
                            category: '客流管理',
                            priority: 'high',
                            description: '客流量呈下降趋势',
                            actions: ['加强营销推广', '优化服务质量', '调整票价策略']
                        });
                    }

                    // 基于异常检测的建议
                    analysis.anomalyDetection.forEach(anomaly => {
                        if (anomaly.severity === 'high') {
                            recommendations.push({
                                category: '异常处理',
                                priority: 'urgent',
                                description: anomaly.description,
                                actions: ['立即调查原因', '制定应对措施', '加强监控']
                            });
                        }
                    });

                    // 基于效率分析的建议
                    const lowEfficiencyLines = this.efficiencyData.filter(line => line.efficiency < 90);
                    if (lowEfficiencyLines.length > 0) {
                        recommendations.push({
                            category: '运行效率',
                            priority: 'medium',
                            description: `${lowEfficiencyLines.map(line => line.line).join('、')}运行效率偏低`,
                            actions: ['优化调度计划', '加强设备维护', '提升服务水平']
                        });
                    }

                    return recommendations;
                },
                initStatisticsCharts() {
                    // 初始化所有统计图表
                    this.initPassengerTrendChart();
                    this.initPassengerLineChart();
                    this.initPassengerPeakChart();
                    this.initEfficiencyComparisonChart();
                    this.initPunctualityTrendChart();
                    this.initRevenueTrendChart();
                    this.initRevenueLineChart();
                    this.initEquipmentGaugeChart();
                    this.initEquipmentStatusChart();
                },
                initPassengerTrendChart() {
                    if (document.getElementById('passenger-trend-chart')) {
                        const chart = echarts.init(document.getElementById('passenger-trend-chart'));
                        chart.setOption({
                            tooltip: {
                                trigger: 'axis',
                                formatter: function(params) {
                                    const data = params[0];
                                    const isWorkday = data.data.workday ? '工作日' : '休息日';
                                    return `${data.name}<br/>${isWorkday}<br/>客流量: ${data.value.toLocaleString()}人次`;
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: this.passengerData.dailyTrend.map(item => item.date)
                            },
                            yAxis: {
                                type: 'value',
                                axisLabel: {
                                    formatter: value => (value / 10000).toFixed(0) + '万'
                                }
                            },
                            series: [{
                                name: '客流量',
                                type: 'line',
                                data: this.passengerData.dailyTrend.map(item => ({
                                    value: item.count,
                                    workday: item.workday
                                })),
                                smooth: true,
                                areaStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                                        { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                                    ])
                                },
                                itemStyle: { color: '#409EFF' }
                            }]
                        });
                        this.statisticsCharts.push(chart);
                    }
                },
                initPassengerLineChart() {
                    if (document.getElementById('passenger-line-chart')) {
                        const chart = echarts.init(document.getElementById('passenger-line-chart'));
                        chart.setOption({
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b}: {c}人次 ({d}%)'
                            },
                            legend: {
                                orient: 'vertical',
                                left: 'left',
                                data: this.passengerData.lineDistribution.map(item => item.line)
                            },
                            series: [{
                                name: '客流分布',
                                type: 'pie',
                                radius: '60%',
                                center: ['60%', '50%'],
                                data: this.passengerData.lineDistribution.map(item => ({
                                    name: item.line,
                                    value: item.count
                                })),
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }]
                        });
                        this.statisticsCharts.push(chart);
                    }
                },
                initPassengerPeakChart() {
                    if (document.getElementById('passenger-peak-chart')) {
                        const chart = echarts.init(document.getElementById('passenger-peak-chart'));
                        chart.setOption({
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: { type: 'shadow' }
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: this.passengerData.peakHours.map(item => item.hour)
                            },
                            yAxis: {
                                type: 'value',
                                axisLabel: {
                                    formatter: value => (value / 1000).toFixed(0) + 'K'
                                }
                            },
                            series: [{
                                name: '客流量',
                                type: 'bar',
                                data: this.passengerData.peakHours.map(item => item.count),
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        { offset: 0, color: '#67C23A' },
                                        { offset: 1, color: '#95D475' }
                                    ])
                                }
                            }]
                        });
                        this.statisticsCharts.push(chart);
                    }
                },
                initEfficiencyComparisonChart() {
                    if (document.getElementById('efficiency-comparison-chart')) {
                        const chart = echarts.init(document.getElementById('efficiency-comparison-chart'));
                        chart.setOption({
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: { type: 'shadow' }
                            },
                            legend: {
                                data: ['准点率', '满载率', '运行效率'],
                                top: 'bottom'
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: this.efficiencyData.map(item => item.line)
                            },
                            yAxis: {
                                type: 'value',
                                min: 75,
                                max: 100,
                                axisLabel: {
                                    formatter: '{value}%'
                                }
                            },
                            series: [
                                {
                                    name: '准点率',
                                    type: 'bar',
                                    data: this.efficiencyData.map(item => item.punctuality),
                                    itemStyle: { color: '#409EFF' }
                                },
                                {
                                    name: '满载率',
                                    type: 'bar',
                                    data: this.efficiencyData.map(item => item.loadFactor),
                                    itemStyle: { color: '#67C23A' }
                                },
                                {
                                    name: '运行效率',
                                    type: 'bar',
                                    data: this.efficiencyData.map(item => item.efficiency),
                                    itemStyle: { color: '#E6A23C' }
                                }
                            ]
                        });
                        this.statisticsCharts.push(chart);
                    }
                },
                initPunctualityTrendChart() {
                    if (document.getElementById('punctuality-trend-chart')) {
                        const chart = echarts.init(document.getElementById('punctuality-trend-chart'));
                        chart.setOption({
                            tooltip: {
                                trigger: 'axis'
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: ['11-16', '11-17', '11-18', '11-19', '11-20', '11-21', '11-22']
                            },
                            yAxis: {
                                type: 'value',
                                min: 95,
                                max: 100,
                                axisLabel: {
                                    formatter: '{value}%'
                                }
                            },
                            series: [{
                                name: '准点率',
                                type: 'line',
                                data: [98.2, 97.8, 98.5, 99.1, 98.7, 97.9, 98.6],
                                smooth: true,
                                itemStyle: { color: '#67C23A' },
                                areaStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
                                        { offset: 1, color: 'rgba(103, 194, 58, 0.1)' }
                                    ])
                                }
                            }]
                        });
                        this.statisticsCharts.push(chart);
                    }
                },
                initRevenueTrendChart() {
                    if (document.getElementById('revenue-trend-chart')) {
                        const chart = echarts.init(document.getElementById('revenue-trend-chart'));
                        chart.setOption({
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: { type: 'cross' }
                            },
                            legend: {
                                data: ['营收', '客流量'],
                                top: 'bottom'
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: this.revenueData.monthlyTrend.map(item => item.month)
                            },
                            yAxis: [
                                {
                                    type: 'value',
                                    name: '营收(万元)',
                                    position: 'left',
                                    axisLabel: {
                                        formatter: value => (value / 10000).toFixed(0)
                                    }
                                },
                                {
                                    type: 'value',
                                    name: '客流量(万人次)',
                                    position: 'right',
                                    axisLabel: {
                                        formatter: value => (value / 10000).toFixed(0)
                                    }
                                }
                            ],
                            series: [
                                {
                                    name: '营收',
                                    type: 'bar',
                                    data: this.revenueData.monthlyTrend.map(item => item.revenue),
                                    itemStyle: { color: '#409EFF' }
                                },
                                {
                                    name: '客流量',
                                    type: 'line',
                                    yAxisIndex: 1,
                                    data: this.revenueData.monthlyTrend.map(item => item.passengers),
                                    itemStyle: { color: '#67C23A' }
                                }
                            ]
                        });
                        this.statisticsCharts.push(chart);
                    }
                },
                initRevenueLineChart() {
                    if (document.getElementById('revenue-line-chart')) {
                        const chart = echarts.init(document.getElementById('revenue-line-chart'));
                        chart.setOption({
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b}: ¥{c} ({d}%)'
                            },
                            legend: {
                                orient: 'vertical',
                                left: 'left',
                                data: this.revenueData.lineRevenue.map(item => item.line)
                            },
                            series: [{
                                name: '收入分布',
                                type: 'pie',
                                radius: ['40%', '70%'],
                                center: ['60%', '50%'],
                                data: this.revenueData.lineRevenue.map(item => ({
                                    name: item.line,
                                    value: item.revenue
                                })),
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                },
                                label: {
                                    formatter: '{b}: {d}%'
                                }
                            }]
                        });
                        this.statisticsCharts.push(chart);
                    }
                },
                initEquipmentGaugeChart() {
                    if (document.getElementById('equipment-gauge-chart')) {
                        const chart = echarts.init(document.getElementById('equipment-gauge-chart'));
                        const avgUtilization = this.equipmentData.reduce((sum, item) => sum + item.utilization, 0) / this.equipmentData.length;

                        chart.setOption({
                            tooltip: {
                                formatter: '{a} <br/>{b} : {c}%'
                            },
                            series: [{
                                name: '设备利用率',
                                type: 'gauge',
                                detail: { formatter: '{value}%' },
                                data: [{ value: avgUtilization.toFixed(1), name: '平均利用率' }],
                                axisLine: {
                                    lineStyle: {
                                        width: 20,
                                        color: [
                                            [0.6, '#F56C6C'],
                                            [0.8, '#E6A23C'],
                                            [1, '#67C23A']
                                        ]
                                    }
                                },
                                pointer: {
                                    itemStyle: {
                                        color: 'auto'
                                    }
                                }
                            }]
                        });
                        this.statisticsCharts.push(chart);
                    }
                },
                initEquipmentStatusChart() {
                    if (document.getElementById('equipment-status-chart')) {
                        const chart = echarts.init(document.getElementById('equipment-status-chart'));
                        const statusCount = {};
                        this.equipmentData.forEach(item => {
                            statusCount[item.status] = (statusCount[item.status] || 0) + 1;
                        });

                        chart.setOption({
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b}: {c} ({d}%)'
                            },
                            legend: {
                                orient: 'vertical',
                                left: 'left',
                                data: Object.keys(statusCount)
                            },
                            series: [{
                                name: '设备状态',
                                type: 'pie',
                                radius: '60%',
                                center: ['60%', '50%'],
                                data: Object.entries(statusCount).map(([status, count]) => ({
                                    name: status,
                                    value: count
                                })),
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }]
                        });
                        this.statisticsCharts.push(chart);
                    }
                },
                changeTimeRange(range) {
                    this.timeRange = range;
                    this.loading = true;
                    // 模拟数据重新加载
                    setTimeout(() => {
                        this.loading = false;
                        this.loadStatisticsData();
                    }, 500);
                },
                getTrendIcon(trend) {
                    return trend === 'positive' ? 'el-icon-top' : 'el-icon-bottom';
                },
                getEfficiencyColor(efficiency) {
                    if (efficiency >= 95) return '#67C23A';
                    if (efficiency >= 90) return '#E6A23C';
                    return '#F56C6C';
                },
                getUtilizationColor(utilization) {
                    if (utilization >= 95) return '#67C23A';
                    if (utilization >= 85) return '#E6A23C';
                    return '#F56C6C';
                },
                getStatusType(status) {
                    const typeMap = {
                        '运行中': 'success',
                        '正常': 'success',
                        '维护中': 'warning',
                        '故障': 'danger'
                    };
                    return typeMap[status] || 'info';
                },
                formatNumber(num) {
                    return num.toLocaleString();
                },
                viewEquipmentDetail(equipment) {
                    this.$alert(
                        `<div style="text-align: left;">
                            <p><strong>设备名称：</strong>${equipment.equipment}</p>
                            <p><strong>设备类型：</strong>${equipment.type}</p>
                            <p><strong>利用率：</strong>${equipment.utilization}%</p>
                            <p><strong>运行状态：</strong>${equipment.status}</p>
                            <p><strong>上次维护：</strong>${equipment.lastMaintenance}</p>
                            <p><strong>下次维护：</strong>${equipment.nextMaintenance}</p>
                            <p><strong>维护成本：</strong>${equipment.maintenanceCost}</p>
                        </div>`,
                        '设备详情',
                        {
                            dangerouslyUseHTMLString: true,
                            confirmButtonText: '确定'
                        }
                    );
                },
                scheduleMaintenace(equipment) {
                    this.$prompt('请输入维护计划日期', '安排维护', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        inputPattern: /^\d{4}-\d{2}-\d{2}$/,
                        inputErrorMessage: '请输入正确的日期格式(YYYY-MM-DD)'
                    }).then(({ value }) => {
                        equipment.nextMaintenance = value;
                        this.$message({
                            type: 'success',
                            message: `已安排${equipment.equipment}在${value}进行维护`
                        });
                    }).catch(() => {
                        this.$message({
                            type: 'info',
                            message: '已取消维护安排'
                        });
                    });
                },
                exportData(format) {
                    // 数据导出功能
                    const exportData = {
                        exportTime: new Date().toLocaleString('zh-CN'),
                        timeRange: this.timeRange,
                        coreMetrics: this.coreMetrics,
                        passengerData: this.passengerData,
                        efficiencyData: this.efficiencyData,
                        revenueData: this.revenueData,
                        equipmentData: this.equipmentData
                    };

                    if (format === 'excel') {
                        this.exportToExcel(exportData);
                    } else if (format === 'pdf') {
                        this.exportToPDF(exportData);
                    } else if (format === 'image') {
                        this.exportChartsAsImages();
                    }
                },
                exportToExcel(data) {
                    // 生成Excel格式的数据
                    const excelData = this.generateExcelData(data);

                    // 创建CSV格式数据（简化的Excel导出）
                    let csvContent = '';

                    // 添加核心指标
                    csvContent += '核心指标\n';
                    csvContent += '指标名称,数值,变化率\n';
                    data.coreMetrics.forEach(metric => {
                        csvContent += `${metric.label},${metric.value},${metric.change}\n`;
                    });
                    csvContent += '\n';

                    // 添加客流数据
                    csvContent += '日客流量统计\n';
                    csvContent += '日期,客流量,是否工作日\n';
                    data.passengerData.dailyTrend.forEach(item => {
                        csvContent += `${item.date},${item.count},${item.workday ? '是' : '否'}\n`;
                    });
                    csvContent += '\n';

                    // 添加线路效率数据
                    csvContent += '线路运行效率\n';
                    csvContent += '线路,准点率(%),满载率(%),平均速度(km/h),运行效率(%),日班次,总里程(km),能耗(kWh)\n';
                    data.efficiencyData.forEach(item => {
                        csvContent += `${item.line},${item.punctuality},${item.loadFactor},${item.avgSpeed},${item.efficiency},${item.dailyTrips},${item.totalDistance},${item.energyConsumption}\n`;
                    });
                    csvContent += '\n';

                    // 添加设备数据
                    csvContent += '设备利用率统计\n';
                    csvContent += '设备名称,设备类型,利用率(%),运行状态,上次维护,下次维护,维护成本\n';
                    data.equipmentData.forEach(item => {
                        csvContent += `${item.equipment},${item.type},${item.utilization},${item.status},${item.lastMaintenance},${item.nextMaintenance},${item.maintenanceCost}\n`;
                    });

                    // 创建下载
                    const BOM = '\uFEFF'; // 添加BOM以支持中文
                    const dataBlob = new Blob([BOM + csvContent], {type: 'text/csv;charset=utf-8'});
                    const url = URL.createObjectURL(dataBlob);

                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `运营数据统计_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.csv`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    URL.revokeObjectURL(url);

                    this.$message({
                        message: '数据已导出为CSV格式（可用Excel打开）',
                        type: 'success'
                    });
                },
                generateExcelData(data) {
                    // 生成结构化的Excel数据
                    return {
                        sheets: [
                            {
                                name: '核心指标',
                                data: data.coreMetrics.map(metric => ({
                                    '指标名称': metric.label,
                                    '数值': metric.value,
                                    '变化率': metric.change,
                                    '趋势': metric.trend === 'positive' ? '上升' : '下降'
                                }))
                            },
                            {
                                name: '客流统计',
                                data: data.passengerData.dailyTrend.map(item => ({
                                    '日期': item.date,
                                    '客流量': item.count,
                                    '是否工作日': item.workday ? '是' : '否'
                                }))
                            },
                            {
                                name: '运行效率',
                                data: data.efficiencyData.map(item => ({
                                    '线路': item.line,
                                    '准点率(%)': item.punctuality,
                                    '满载率(%)': item.loadFactor,
                                    '平均速度(km/h)': item.avgSpeed,
                                    '运行效率(%)': item.efficiency,
                                    '日班次': item.dailyTrips,
                                    '总里程(km)': item.totalDistance,
                                    '能耗(kWh)': item.energyConsumption
                                }))
                            },
                            {
                                name: '设备状态',
                                data: data.equipmentData.map(item => ({
                                    '设备名称': item.equipment,
                                    '设备类型': item.type,
                                    '利用率(%)': item.utilization,
                                    '运行状态': item.status,
                                    '上次维护': item.lastMaintenance,
                                    '下次维护': item.nextMaintenance,
                                    '维护成本': item.maintenanceCost
                                }))
                            }
                        ]
                    };
                },
                exportToPDF(data) {
                    // 生成PDF报告
                    const pdfContent = this.generatePDFContent(data);

                    // 创建HTML内容用于PDF生成
                    const htmlContent = `
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <meta charset="UTF-8">
                            <title>运营数据统计报告</title>
                            <style>
                                body { font-family: 'Microsoft YaHei', sans-serif; margin: 20px; }
                                .header { text-align: center; margin-bottom: 30px; }
                                .title { font-size: 24px; font-weight: bold; color: #333; }
                                .subtitle { font-size: 14px; color: #666; margin-top: 10px; }
                                .section { margin-bottom: 30px; }
                                .section-title { font-size: 18px; font-weight: bold; color: #409EFF; border-bottom: 2px solid #409EFF; padding-bottom: 5px; margin-bottom: 15px; }
                                .metric-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px; margin-bottom: 20px; }
                                .metric-card { border: 1px solid #eee; padding: 15px; border-radius: 5px; text-align: center; }
                                .metric-value { font-size: 20px; font-weight: bold; color: #409EFF; }
                                .metric-label { font-size: 12px; color: #666; margin-top: 5px; }
                                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                                th { background-color: #f5f5f5; font-weight: bold; }
                                .footer { text-align: center; margin-top: 50px; font-size: 12px; color: #999; }
                            </style>
                        </head>
                        <body>
                            ${pdfContent}
                        </body>
                        </html>
                    `;

                    // 创建新窗口用于打印
                    const printWindow = window.open('', '_blank');
                    printWindow.document.write(htmlContent);
                    printWindow.document.close();

                    // 等待内容加载完成后打印
                    setTimeout(() => {
                        printWindow.print();
                        this.$message({
                            message: 'PDF报告已生成，请在打印对话框中选择"另存为PDF"',
                            type: 'success',
                            duration: 5000
                        });
                    }, 1000);
                },
                generatePDFContent(data) {
                    // 生成PDF内容
                    const currentDate = new Date().toLocaleDateString('zh-CN');
                    const analysis = this.performDataAnalysis();

                    return `
                        <div class="header">
                            <div class="title">轨道交通运输列车运行监测系统</div>
                            <div class="title">运营数据统计报告</div>
                            <div class="subtitle">报告生成时间: ${currentDate} | 统计周期: ${this.getTimeRangeText()}</div>
                        </div>

                        <div class="section">
                            <div class="section-title">核心指标概览</div>
                            <div class="metric-grid">
                                ${data.coreMetrics.map(metric => `
                                    <div class="metric-card">
                                        <div class="metric-value">${metric.value}</div>
                                        <div class="metric-label">${metric.label}</div>
                                        <div style="color: ${metric.trend === 'positive' ? '#67C23A' : '#F56C6C'}; font-size: 12px;">
                                            ${metric.change}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>

                        <div class="section">
                            <div class="section-title">客流分析</div>
                            <table>
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>客流量(人次)</th>
                                        <th>类型</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.passengerData.dailyTrend.map(item => `
                                        <tr>
                                            <td>${item.date}</td>
                                            <td>${item.count.toLocaleString()}</td>
                                            <td>${item.workday ? '工作日' : '休息日'}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        <div class="section">
                            <div class="section-title">运行效率分析</div>
                            <table>
                                <thead>
                                    <tr>
                                        <th>线路</th>
                                        <th>准点率(%)</th>
                                        <th>满载率(%)</th>
                                        <th>运行效率(%)</th>
                                        <th>日班次</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.efficiencyData.map(item => `
                                        <tr>
                                            <td>${item.line}</td>
                                            <td>${item.punctuality}</td>
                                            <td>${item.loadFactor}</td>
                                            <td>${item.efficiency}</td>
                                            <td>${item.dailyTrips}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>

                        <div class="section">
                            <div class="section-title">数据分析结论</div>
                            <div style="margin-bottom: 15px;">
                                <strong>趋势分析:</strong>
                                <ul>
                                    <li>客流趋势: ${analysis.trendAnalysis.passenger.direction === 'increasing' ? '上升' : analysis.trendAnalysis.passenger.direction === 'decreasing' ? '下降' : '稳定'}</li>
                                    <li>收入趋势: ${analysis.trendAnalysis.revenue.direction === 'increasing' ? '上升' : analysis.trendAnalysis.revenue.direction === 'decreasing' ? '下降' : '稳定'}</li>
                                </ul>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <strong>异常检测:</strong>
                                ${analysis.anomalyDetection.length > 0 ?
                                    `<ul>${analysis.anomalyDetection.map(anomaly => `<li>${anomaly.description}</li>`).join('')}</ul>` :
                                    '<p>未发现明显异常</p>'
                                }
                            </div>
                            <div>
                                <strong>改进建议:</strong>
                                <ul>
                                    ${analysis.recommendations.map(rec => `<li>${rec.description}: ${rec.actions.join('、')}</li>`).join('')}
                                </ul>
                            </div>
                        </div>

                        <div class="footer">
                            <p>本报告由轨道交通运输列车运行监测系统自动生成</p>
                            <p>报告生成时间: ${new Date().toLocaleString('zh-CN')}</p>
                        </div>
                    `;
                },
                getTimeRangeText() {
                    const rangeMap = {
                        'today': '今日',
                        'week': '本周',
                        'month': '本月',
                        'year': '本年'
                    };
                    return rangeMap[this.timeRange] || '本月';
                },
                exportChartsAsImages() {
                    // 导出图表为图片
                    if (!this.statisticsCharts || this.statisticsCharts.length === 0) {
                        this.$message({
                            message: '没有可导出的图表',
                            type: 'warning'
                        });
                        return;
                    }

                    const zip = new JSZip();
                    let exportCount = 0;

                    this.statisticsCharts.forEach((chart, index) => {
                        if (chart && chart.getDataURL) {
                            try {
                                const dataURL = chart.getDataURL({
                                    type: 'png',
                                    pixelRatio: 2,
                                    backgroundColor: '#fff'
                                });

                                // 将base64转换为blob
                                const base64Data = dataURL.split(',')[1];
                                const binaryData = atob(base64Data);
                                const arrayBuffer = new ArrayBuffer(binaryData.length);
                                const uint8Array = new Uint8Array(arrayBuffer);

                                for (let i = 0; i < binaryData.length; i++) {
                                    uint8Array[i] = binaryData.charCodeAt(i);
                                }

                                zip.file(`图表_${index + 1}.png`, arrayBuffer);
                                exportCount++;
                            } catch (error) {
                                console.error('导出图表失败:', error);
                            }
                        }
                    });

                    if (exportCount > 0) {
                        zip.generateAsync({type: 'blob'}).then(content => {
                            const url = URL.createObjectURL(content);
                            const link = document.createElement('a');
                            link.href = url;
                            link.download = `统计图表_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.zip`;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                            URL.revokeObjectURL(url);

                            this.$message({
                                message: `已导出${exportCount}个图表`,
                                type: 'success'
                            });
                        });
                    } else {
                        this.$message({
                            message: '图表导出失败',
                            type: 'error'
                        });
                    }
                },
                // 自动化报表生成
                generateAutomaticReport() {
                    // 生成自动化报表
                    const reportData = {
                        reportInfo: {
                            title: '运营数据自动分析报告',
                            generateTime: new Date().toLocaleString('zh-CN'),
                            period: this.timeRange,
                            version: '1.0'
                        },
                        executiveSummary: this.generateExecutiveSummary(),
                        dataAnalysis: this.performDataAnalysis(),
                        kpiAnalysis: this.analyzeKPIs(),
                        operationalInsights: this.generateOperationalInsights(),
                        recommendations: this.generateDetailedRecommendations(),
                        appendix: {
                            rawData: {
                                coreMetrics: this.coreMetrics,
                                passengerData: this.passengerData,
                                efficiencyData: this.efficiencyData,
                                revenueData: this.revenueData,
                                equipmentData: this.equipmentData
                            }
                        }
                    };

                    return reportData;
                },
                generateExecutiveSummary() {
                    // 生成执行摘要
                    const totalPassengers = this.passengerData.dailyTrend.reduce((sum, item) => sum + item.count, 0);
                    const avgPunctuality = this.efficiencyData.reduce((sum, item) => sum + item.punctuality, 0) / this.efficiencyData.length;
                    const totalRevenue = this.revenueData.total;
                    const avgUtilization = this.equipmentData.reduce((sum, item) => sum + item.utilization, 0) / this.equipmentData.length;

                    return {
                        overview: `本期运营数据显示，系统整体运行状况良好。总客流量达到${totalPassengers.toLocaleString()}人次，平均准点率为${avgPunctuality.toFixed(1)}%，总营收为¥${totalRevenue.toLocaleString()}，设备平均利用率为${avgUtilization.toFixed(1)}%。`,
                        keyFindings: [
                            `客流量${this.getPassengerTrend()}，主要集中在工作日高峰时段`,
                            `运行效率整体${this.getEfficiencyTrend()}，${this.getBestPerformingLine()}表现最佳`,
                            `收入增长${this.getRevenueTrend()}，主要得益于客流量增长`,
                            `设备利用率${this.getUtilizationTrend()}，需要关注维护计划`
                        ],
                        riskAlerts: this.identifyRiskAlerts()
                    };
                },
                getPassengerTrend() {
                    const trend = this.analyzeTrends().passenger.direction;
                    return trend === 'increasing' ? '呈上升趋势' : trend === 'decreasing' ? '呈下降趋势' : '保持稳定';
                },
                getEfficiencyTrend() {
                    const avgEfficiency = this.efficiencyData.reduce((sum, item) => sum + item.efficiency, 0) / this.efficiencyData.length;
                    return avgEfficiency >= 92 ? '表现优秀' : avgEfficiency >= 88 ? '表现良好' : '有待提升';
                },
                getBestPerformingLine() {
                    const bestLine = this.efficiencyData.reduce((best, current) =>
                        current.efficiency > best.efficiency ? current : best
                    );
                    return bestLine.line;
                },
                getRevenueTrend() {
                    const trend = this.analyzeTrends().revenue.direction;
                    return trend === 'increasing' ? '稳步增长' : trend === 'decreasing' ? '有所下降' : '基本稳定';
                },
                getUtilizationTrend() {
                    const avgUtilization = this.equipmentData.reduce((sum, item) => sum + item.utilization, 0) / this.equipmentData.length;
                    return avgUtilization >= 95 ? '保持高位' : avgUtilization >= 90 ? '处于正常水平' : '偏低需关注';
                },
                identifyRiskAlerts() {
                    const alerts = [];
                    const anomalies = this.detectAnomalies();

                    anomalies.forEach(anomaly => {
                        if (anomaly.severity === 'high') {
                            alerts.push(`高风险: ${anomaly.description}`);
                        }
                    });

                    // 检查设备维护风险
                    const maintenanceRisk = this.equipmentData.filter(item => {
                        const nextMaintenance = new Date(item.nextMaintenance);
                        const today = new Date();
                        const daysUntilMaintenance = (nextMaintenance - today) / (1000 * 60 * 60 * 24);
                        return daysUntilMaintenance <= 7;
                    });

                    if (maintenanceRisk.length > 0) {
                        alerts.push(`维护提醒: ${maintenanceRisk.length}台设备即将到达维护期`);
                    }

                    return alerts;
                },
                analyzeKPIs() {
                    // KPI分析
                    return {
                        passengerKPIs: {
                            dailyAverage: Math.round(this.passengerData.dailyTrend.reduce((sum, item) => sum + item.count, 0) / this.passengerData.dailyTrend.length),
                            peakHourRatio: this.calculatePeakHourRatio(),
                            weekdayWeekendRatio: this.calculateWeekdayWeekendRatio(),
                            growthRate: this.calculatePassengerGrowthRate()
                        },
                        operationalKPIs: {
                            systemPunctuality: this.calculateSystemPunctuality(),
                            averageLoadFactor: this.calculateAverageLoadFactor(),
                            energyEfficiency: this.calculateEnergyEfficiency(),
                            serviceReliability: this.calculateServiceReliability()
                        },
                        financialKPIs: {
                            revenuePerPassenger: this.revenueData.avgRevPerPassenger,
                            operatingRatio: this.calculateOperatingRatio(),
                            profitMargin: this.calculateProfitMargin(),
                            costPerKm: this.calculateCostPerKm()
                        }
                    };
                },
                calculatePeakHourRatio() {
                    const peakHours = this.passengerData.peakHours.filter(hour =>
                        hour.hour === '8:00' || hour.hour === '18:00'
                    );
                    const peakTotal = peakHours.reduce((sum, hour) => sum + hour.count, 0);
                    const dailyTotal = this.passengerData.peakHours.reduce((sum, hour) => sum + hour.count, 0);
                    return ((peakTotal / dailyTotal) * 100).toFixed(1);
                },
                calculateWeekdayWeekendRatio() {
                    const weekdayTotal = this.passengerData.dailyTrend
                        .filter(item => item.workday)
                        .reduce((sum, item) => sum + item.count, 0);
                    const weekendTotal = this.passengerData.dailyTrend
                        .filter(item => !item.workday)
                        .reduce((sum, item) => sum + item.count, 0);
                    return (weekdayTotal / weekendTotal).toFixed(2);
                },
                calculatePassengerGrowthRate() {
                    const data = this.passengerData.dailyTrend.map(item => item.count);
                    const firstHalf = data.slice(0, Math.floor(data.length / 2));
                    const secondHalf = data.slice(Math.floor(data.length / 2));
                    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
                    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
                    return (((secondAvg - firstAvg) / firstAvg) * 100).toFixed(2);
                },
                calculateSystemPunctuality() {
                    return (this.efficiencyData.reduce((sum, item) => sum + item.punctuality, 0) / this.efficiencyData.length).toFixed(1);
                },
                calculateAverageLoadFactor() {
                    return (this.efficiencyData.reduce((sum, item) => sum + item.loadFactor, 0) / this.efficiencyData.length).toFixed(1);
                },
                calculateEnergyEfficiency() {
                    const totalDistance = this.efficiencyData.reduce((sum, item) => sum + item.totalDistance, 0);
                    const totalEnergy = this.efficiencyData.reduce((sum, item) => sum + item.energyConsumption, 0);
                    return (totalEnergy / totalDistance).toFixed(2);
                },
                calculateServiceReliability() {
                    // 基于准点率和设备利用率计算服务可靠性
                    const punctuality = this.calculateSystemPunctuality();
                    const utilization = this.equipmentData.reduce((sum, item) => sum + item.utilization, 0) / this.equipmentData.length;
                    return ((parseFloat(punctuality) + utilization) / 2).toFixed(1);
                },
                calculateOperatingRatio() {
                    // 模拟运营比率计算
                    return (Math.random() * 20 + 70).toFixed(1);
                },
                calculateProfitMargin() {
                    // 模拟利润率计算
                    return (Math.random() * 15 + 10).toFixed(1);
                },
                calculateCostPerKm() {
                    // 模拟每公里成本计算
                    return (Math.random() * 5 + 8).toFixed(2);
                },
                generateOperationalInsights() {
                    // 生成运营洞察
                    return {
                        passengerInsights: [
                            '工作日客流量明显高于休息日，建议在工作日增加运力投入',
                            '早晚高峰时段客流集中，需要优化调度安排',
                            '2号线客流量最大，可考虑增加班次密度'
                        ],
                        efficiencyInsights: [
                            '5号线运行效率最高，可作为其他线路的标杆',
                            '6号线准点率相对较低，需要分析原因并改进',
                            '整体满载率较高，说明运力配置基本合理'
                        ],
                        revenueInsights: [
                            '收入与客流量呈正相关，客流增长直接带动收入增长',
                            '平均票价保持稳定，收入增长主要来源于客流增长',
                            '各线路收入分布与客流分布基本一致'
                        ],
                        equipmentInsights: [
                            '设备整体利用率较高，说明资源配置效率良好',
                            '部分设备即将到达维护期，需要提前安排维护计划',
                            '通信设备利用率相对较低，可能存在冗余配置'
                        ]
                    };
                },
                generateDetailedRecommendations() {
                    // 生成详细建议
                    return {
                        immediate: [
                            {
                                category: '运营调度',
                                priority: 'high',
                                description: '优化高峰时段调度安排',
                                actions: ['增加高峰时段班次', '调整发车间隔', '优化人员配置'],
                                expectedImpact: '提升准点率2-3%，减少乘客等待时间',
                                timeline: '1-2周'
                            },
                            {
                                category: '设备维护',
                                priority: 'medium',
                                description: '制定预防性维护计划',
                                actions: ['安排设备检查', '更新维护计划', '准备备件库存'],
                                expectedImpact: '降低故障率15%，提升设备可靠性',
                                timeline: '1个月'
                            }
                        ],
                        shortTerm: [
                            {
                                category: '客流管理',
                                priority: 'medium',
                                description: '实施客流引导策略',
                                actions: ['增加客流信息发布', '优化站台管理', '推广错峰出行'],
                                expectedImpact: '平衡客流分布，提升乘客体验',
                                timeline: '2-3个月'
                            }
                        ],
                        longTerm: [
                            {
                                category: '系统优化',
                                priority: 'low',
                                description: '升级智能调度系统',
                                actions: ['引入AI调度算法', '升级信号系统', '完善数据分析平台'],
                                expectedImpact: '全面提升运营效率10-15%',
                                timeline: '6-12个月'
                            }
                        ]
                    };
                },
                resizeCharts() {
                    // 调整所有图表大小
                    if (this.statisticsCharts) {
                        this.statisticsCharts.forEach(chart => {
                            if (chart && chart.resize) {
                                chart.resize();
                            }
                        });
                    }
                }
            },
            watch: {
                activeTab(newTab) {
                    // 切换标签页时重新调整图表大小
                    this.$nextTick(() => {
                        this.resizeCharts();
                    });
                }
            }
        }
    },
    // 添加系统设置路由
    {
        path: '/settings',
        component: {
            template: '#settings-template',
            data() {
                return {
                    loading: true,
                    activeTab: 'users',
                    // 用户管理相关数据
                    users: [],
                    userSearchQuery: '',
                    userRoleFilter: '',
                    userStatusFilter: '',
                    userCurrentPage: 1,
                    userPageSize: 10,
                    userDialogVisible: false,
                    userDialogTitle: '添加用户',
                    userForm: {
                        id: '',
                        username: '',
                        realName: '',
                        email: '',
                        phone: '',
                        role: '',
                        status: 'active',
                        password: ''
                    },
                    userRules: {
                        username: [
                            { required: true, message: '请输入用户名', trigger: 'blur' },
                            { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
                        ],
                        realName: [
                            { required: true, message: '请输入真实姓名', trigger: 'blur' }
                        ],
                        email: [
                            { required: true, message: '请输入邮箱地址', trigger: 'blur' },
                            { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] }
                        ],
                        role: [
                            { required: true, message: '请选择角色', trigger: 'change' }
                        ],
                        password: [
                            { required: true, message: '请输入密码', trigger: 'blur' },
                            { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
                        ]
                    },
                    // 角色管理相关数据
                    roles: [],
                    // 系统配置相关数据
                    systemConfig: {
                        systemName: '轨道交通运输列车运行监测系统',
                        systemVersion: 'v1.0.0',
                        sessionTimeout: 30,
                        passwordComplexity: true,
                        dataRefreshInterval: 5,
                        thresholds: {
                            temperature: 80,
                            speed: 120,
                            pressure: 5.0
                        },
                        notifications: {
                            email: true,
                            sms: false,
                            system: true
                        },
                        emailConfig: {
                            smtpServer: '',
                            smtpPort: 587,
                            username: '',
                            password: ''
                        }
                    },
                    // 备份管理相关数据
                    backupConfig: {
                        autoBackup: true,
                        frequency: 'daily',
                        backupTime: null,
                        retentionDays: 30
                    },
                    backupFiles: [],
                    // 系统监控相关数据
                    systemMetrics: [],
                    systemLogs: [],
                    logLevelFilter: '',
                    logDateRange: null
                };
            },
            mounted() {
                this.loadSettingsData();
            },
            computed: {
                filteredUsers() {
                    let filtered = this.users;

                    // 搜索过滤
                    if (this.userSearchQuery) {
                        filtered = filtered.filter(user =>
                            user.username.toLowerCase().includes(this.userSearchQuery.toLowerCase()) ||
                            user.realName.toLowerCase().includes(this.userSearchQuery.toLowerCase())
                        );
                    }

                    // 角色过滤
                    if (this.userRoleFilter) {
                        filtered = filtered.filter(user => user.role === this.userRoleFilter);
                    }

                    // 状态过滤
                    if (this.userStatusFilter) {
                        filtered = filtered.filter(user => user.status === this.userStatusFilter);
                    }

                    // 分页
                    const start = (this.userCurrentPage - 1) * this.userPageSize;
                    const end = start + this.userPageSize;
                    return filtered.slice(start, end);
                },
                filteredLogs() {
                    let filtered = this.systemLogs;

                    // 级别过滤
                    if (this.logLevelFilter) {
                        filtered = filtered.filter(log => log.level === this.logLevelFilter);
                    }

                    // 日期范围过滤
                    if (this.logDateRange && this.logDateRange.length === 2) {
                        const startDate = this.logDateRange[0];
                        const endDate = this.logDateRange[1];
                        filtered = filtered.filter(log => {
                            const logDate = new Date(log.time);
                            return logDate >= startDate && logDate <= endDate;
                        });
                    }

                    return filtered;
                }
            },
            methods: {
                loadSettingsData() {
                    this.loading = true;
                    // 模拟数据加载
                    setTimeout(() => {
                        this.loading = false;
                        this.initUsersData();
                        this.initRolesData();
                        this.initBackupData();
                        this.initSystemMetrics();
                        this.initSystemLogs();
                    }, 1000);
                },
                initUsersData() {
                    // 初始化用户数据
                    this.users = [
                        {
                            id: 1,
                            username: 'admin',
                            realName: '系统管理员',
                            email: '<EMAIL>',
                            phone: '13800138000',
                            role: 'admin',
                            status: 'active',
                            lastLogin: '2023-11-22 10:30:00',
                            createTime: '2023-01-01 00:00:00'
                        },
                        {
                            id: 2,
                            username: 'operator1',
                            realName: '张操作员',
                            email: '<EMAIL>',
                            phone: '13800138001',
                            role: 'operator',
                            status: 'active',
                            lastLogin: '2023-11-22 09:15:00',
                            createTime: '2023-02-15 10:00:00'
                        },
                        {
                            id: 3,
                            username: 'viewer1',
                            realName: '李观察员',
                            email: '<EMAIL>',
                            phone: '13800138002',
                            role: 'viewer',
                            status: 'active',
                            lastLogin: '2023-11-21 16:45:00',
                            createTime: '2023-03-10 14:30:00'
                        },
                        {
                            id: 4,
                            username: 'operator2',
                            realName: '王操作员',
                            email: '<EMAIL>',
                            phone: '13800138003',
                            role: 'operator',
                            status: 'disabled',
                            lastLogin: '2023-11-20 11:20:00',
                            createTime: '2023-04-05 09:15:00'
                        }
                    ];
                },
                initRolesData() {
                    // 初始化角色数据
                    this.roles = [
                        {
                            id: 1,
                            name: 'admin',
                            description: '系统管理员，拥有所有权限',
                            userCount: 1,
                            permissions: ['user_manage', 'system_config', 'data_backup', 'monitor_view', 'train_control', 'warning_manage'],
                            isSystem: true,
                            createTime: '2023-01-01 00:00:00'
                        },
                        {
                            id: 2,
                            name: 'operator',
                            description: '操作员，可以进行日常操作',
                            userCount: 2,
                            permissions: ['monitor_view', 'train_control', 'warning_manage', 'data_view'],
                            isSystem: true,
                            createTime: '2023-01-01 00:00:00'
                        },
                        {
                            id: 3,
                            name: 'viewer',
                            description: '观察员，只能查看数据',
                            userCount: 1,
                            permissions: ['monitor_view', 'data_view'],
                            isSystem: true,
                            createTime: '2023-01-01 00:00:00'
                        }
                    ];
                }
            }
        }
    },
    // 其他路由配置...
];

// 创建路由实例
const router = new VueRouter({
    routes
});

// 创建Vue实例
new Vue({
    el: '#app',
    router,
    data: {
        activeMenu: '/',
        currentPage: '监测总览',
        currentTime: '',
        sidebarCollapsed: false
    },
    created() {
        this.updateTime();
        // 每秒更新一次时间
        setInterval(this.updateTime, 1000);
    },
    watch: {
        '$route'(to) {
            this.activeMenu = to.path;
            switch(to.path) {
                case '/':
                    this.currentPage = '监测总览';
                    break;
                case '/train-status':
                    this.currentPage = '列车运行状态';
                    break;
                case '/train-track':
                    this.currentPage = '列车运行轨迹';
                    break;
                case '/warning':
                    this.currentPage = '故障预警';
                    break;
                case '/dispatch':
                    this.currentPage = '车辆调度管理';
                    break;
                case '/data-analysis':
                    this.currentPage = '运行数据分析';
                    break;
                case '/safety':
                    this.currentPage = '安全评估';
                    break;
                case '/statistics':
                    this.currentPage = '运营数据统计';
                    break;
                case '/settings':
                    this.currentPage = '系统设置';
                    break;
            }
        }
    },
    methods: {
        updateTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            this.currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        toggleSidebar() {
            this.sidebarCollapsed = !this.sidebarCollapsed;
            // 切换侧边栏宽度
            const aside = document.querySelector('.el-aside');
            if (aside) {
                aside.style.width = this.sidebarCollapsed ? '64px' : '200px';
            }
        }
    }
}); 