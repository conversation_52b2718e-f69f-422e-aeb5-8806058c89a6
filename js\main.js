// 路由配置
const routes = [
    {
        path: '/',
        component: {
            template: '#dashboard-template',
            data() {
                return {
                    loading: true,
                    statistics: {
                        totalTrains: 0,
                        runningTrains: 0,
                        warningCount: 0,
                        safetyScore: 0
                    },
                    recentWarnings: [],
                    trainStatusData: []
                };
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.statistics = {
                        totalTrains: 128,
                        runningTrains: 87,
                        warningCount: 3,
                        safetyScore: 96.8
                    };
                    this.recentWarnings = [
                        { id: 'W2023112201', trainId: 'T1208', type: '温度异常', level: '中', time: '2023-11-22 08:32:15', status: '已处理' },
                        { id: 'W2023112105', trainId: 'T0537', type: '制动系统异常', level: '高', time: '2023-11-21 15:47:23', status: '处理中' },
                        { id: 'W2023112003', trainId: 'T0912', type: '信号干扰', level: '低', time: '2023-11-20 10:15:42', status: '已处理' }
                    ];
                    this.trainStatusData = [
                        { trainId: 'T1208', line: '1号线', status: '正常运行', speed: '60km/h', position: '西单站-复兴门站', nextStation: '复兴门站', arrivalTime: '2分钟' },
                        { trainId: 'T0537', line: '2号线', status: '减速运行', speed: '45km/h', position: '朝阳门站-东四十条站', nextStation: '东四十条站', arrivalTime: '4分钟' },
                        { trainId: 'T0912', line: '4号线', status: '正常运行', speed: '58km/h', position: '北京南站-陶然亭站', nextStation: '陶然亭站', arrivalTime: '3分钟' }
                    ];
                    this.initCharts();
                }, 1000);
            },
            methods: {
                initCharts() {
                    // 初始化列车运行状态统计图表
                    const statusChart = echarts.init(document.getElementById('train-status-chart'));
                    statusChart.setOption({
                        title: {
                            text: '列车运行状态分布',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c} ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            left: 'left',
                            data: ['正常运行', '减速运行', '临时停车', '检修中', '未运行']
                        },
                        series: [
                            {
                                name: '运行状态',
                                type: 'pie',
                                radius: '60%',
                                center: ['50%', '50%'],
                                data: [
                                    { value: 78, name: '正常运行' },
                                    { value: 9, name: '减速运行' },
                                    { value: 3, name: '临时停车' },
                                    { value: 12, name: '检修中' },
                                    { value: 26, name: '未运行' }
                                ],
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    });

                    // 初始化故障类型统计图表
                    const warningChart = echarts.init(document.getElementById('warning-chart'));
                    warningChart.setOption({
                        title: {
                            text: '近30天故障类型统计',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        legend: {
                            data: ['低级故障', '中级故障', '高级故障'],
                            top: 'bottom'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: ['信号系统', '动力系统', '制动系统', '车门系统', '空调系统', '监控系统', '其他']
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [
                            {
                                name: '低级故障',
                                type: 'bar',
                                stack: 'total',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: [12, 8, 5, 7, 9, 3, 4]
                            },
                            {
                                name: '中级故障',
                                type: 'bar',
                                stack: 'total',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: [5, 4, 3, 2, 3, 1, 2]
                            },
                            {
                                name: '高级故障',
                                type: 'bar',
                                stack: 'total',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: [1, 2, 3, 0, 1, 0, 1]
                            }
                        ]
                    });

                    // 初始化运行效率统计图表
                    const efficiencyChart = echarts.init(document.getElementById('efficiency-chart'));
                    efficiencyChart.setOption({
                        title: {
                            text: '近7天运行效率统计',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        legend: {
                            data: ['准点率', '满载率', '运行效率'],
                            top: 'bottom'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            boundaryGap: false,
                            data: ['11-16', '11-17', '11-18', '11-19', '11-20', '11-21', '11-22']
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                formatter: '{value}%'
                            }
                        },
                        series: [
                            {
                                name: '准点率',
                                type: 'line',
                                data: [98.2, 97.8, 98.5, 99.1, 98.7, 97.9, 98.4]
                            },
                            {
                                name: '满载率',
                                type: 'line',
                                data: [85.3, 87.2, 86.5, 88.7, 90.1, 89.5, 88.2]
                            },
                            {
                                name: '运行效率',
                                type: 'line',
                                data: [92.5, 91.8, 93.2, 94.5, 93.8, 92.7, 93.5]
                            }
                        ]
                    });

                    // 监听窗口大小变化，重新调整图表大小
                    window.addEventListener('resize', function() {
                        statusChart.resize();
                        warningChart.resize();
                        efficiencyChart.resize();
                    });
                },
                getStatusClass(status) {
                    if (status === '正常运行') return 'status-normal';
                    if (status === '减速运行') return 'status-warning';
                    if (status === '临时停车') return 'status-danger';
                    return '';
                },
                getWarningLevelClass(level) {
                    if (level === '高') return 'warning-level-high';
                    if (level === '中') return 'warning-level-medium';
                    if (level === '低') return 'warning-level-low';
                    return '';
                }
            }
        }
    },
    {
        path: '/train-status',
        component: {
            template: '#train-status-template',
            data() {
                return {
                    loading: true,
                    trainList: [],
                    searchQuery: '',
                    filterLine: '',
                    filterStatus: ''
                };
            },
            computed: {
                filteredTrains() {
                    return this.trainList.filter(train => {
                        const matchQuery = train.trainId.toLowerCase().includes(this.searchQuery.toLowerCase()) || 
                                          train.line.toLowerCase().includes(this.searchQuery.toLowerCase());
                        const matchLine = this.filterLine ? train.line === this.filterLine : true;
                        const matchStatus = this.filterStatus ? train.status === this.filterStatus : true;
                        return matchQuery && matchLine && matchStatus;
                    });
                }
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.trainList = [
                        { trainId: 'T1208', line: '1号线', status: '正常运行', speed: '60km/h', position: '西单站-复兴门站', nextStation: '复兴门站', arrivalTime: '2分钟', temperature: '23°C', humidity: '45%', passengers: '中等' },
                        { trainId: 'T0537', line: '2号线', status: '减速运行', speed: '45km/h', position: '朝阳门站-东四十条站', nextStation: '东四十条站', arrivalTime: '4分钟', temperature: '24°C', humidity: '48%', passengers: '较多' },
                        { trainId: 'T0912', line: '4号线', status: '正常运行', speed: '58km/h', position: '北京南站-陶然亭站', nextStation: '陶然亭站', arrivalTime: '3分钟', temperature: '22°C', humidity: '42%', passengers: '较少' },
                        { trainId: 'T0315', line: '5号线', status: '正常运行', speed: '62km/h', position: '惠新西街南口站-和平西桥站', nextStation: '和平西桥站', arrivalTime: '2分钟', temperature: '23°C', humidity: '44%', passengers: '中等' },
                        { trainId: 'T0721', line: '6号线', status: '临时停车', speed: '0km/h', position: '草房站', nextStation: '常营站', arrivalTime: '未知', temperature: '25°C', humidity: '50%', passengers: '较多' }
                    ];
                    this.initMap();
                }, 1000);
            },
            methods: {
                initMap() {
                    // 模拟地图初始化，实际项目中可能使用百度地图、高德地图等API
                    console.log('初始化列车运行地图');
                },
                getStatusClass(status) {
                    if (status === '正常运行') return 'status-normal';
                    if (status === '减速运行') return 'status-warning';
                    if (status === '临时停车') return 'status-danger';
                    return '';
                },
                viewTrainDetail(train) {
                    // 查看列车详情
                    console.log('查看列车详情:', train);
                }
            }
        }
    },
    {
        path: '/train-track',
        component: {
            template: '#train-track-template',
            data() {
                return {
                    loading: true,
                    selectedTrain: '',
                    trainOptions: [],
                    trackData: []
                };
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.trainOptions = [
                        { value: 'T1208', label: 'T1208 (1号线)' },
                        { value: 'T0537', label: 'T0537 (2号线)' },
                        { value: 'T0912', label: 'T0912 (4号线)' },
                        { value: 'T0315', label: 'T0315 (5号线)' },
                        { value: 'T0721', label: 'T0721 (6号线)' }
                    ];
                }, 1000);
            },
            methods: {
                loadTrackData() {
                    if (!this.selectedTrain) return;
                    
                    this.loading = true;
                    // 模拟加载轨迹数据
                    setTimeout(() => {
                        this.loading = false;
                        this.trackData = [
                            { time: '08:00:00', station: '西直门站', status: '正常停靠', speed: '0km/h', passengers: '上车45人，下车32人' },
                            { time: '08:02:30', station: '西直门站-大钟寺站', status: '正常运行', speed: '60km/h', passengers: '约180人' },
                            { time: '08:05:00', station: '大钟寺站', status: '正常停靠', speed: '0km/h', passengers: '上车28人，下车15人' },
                            { time: '08:07:30', station: '大钟寺站-知春路站', status: '正常运行', speed: '58km/h', passengers: '约195人' },
                            { time: '08:10:00', station: '知春路站', status: '正常停靠', speed: '0km/h', passengers: '上车35人，下车22人' },
                            { time: '08:12:30', station: '知春路站-五道口站', status: '正常运行', speed: '62km/h', passengers: '约210人' },
                            { time: '08:15:00', station: '五道口站', status: '正常停靠', speed: '0km/h', passengers: '上车42人，下车38人' }
                        ];
                        this.initTrackMap();
                    }, 1000);
                },
                initTrackMap() {
                    // 模拟轨迹地图初始化
                    console.log('初始化列车轨迹地图');
                }
            }
        }
    },
    {
        path: '/warning',
        component: {
            template: '#warning-template',
            data() {
                return {
                    loading: true,
                    warningList: [],
                    searchQuery: '',
                    filterLevel: '',
                    filterStatus: '',
                    dateRange: ''
                };
            },
            computed: {
                filteredWarnings() {
                    return this.warningList.filter(warning => {
                        const matchQuery = warning.id.toLowerCase().includes(this.searchQuery.toLowerCase()) || 
                                          warning.trainId.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                                          warning.type.toLowerCase().includes(this.searchQuery.toLowerCase());
                        const matchLevel = this.filterLevel ? warning.level === this.filterLevel : true;
                        const matchStatus = this.filterStatus ? warning.status === this.filterStatus : true;
                        return matchQuery && matchLevel && matchStatus;
                    });
                }
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.warningList = [
                        { id: 'W2023112201', trainId: 'T1208', type: '温度异常', level: '中', time: '2023-11-22 08:32:15', location: '西单站-复兴门站', description: '电机温度超过正常范围', status: '已处理', handler: '张工', handleTime: '2023-11-22 09:15:22' },
                        { id: 'W2023112105', trainId: 'T0537', type: '制动系统异常', level: '高', time: '2023-11-21 15:47:23', location: '朝阳门站-东四十条站', description: '制动系统压力异常波动', status: '处理中', handler: '李工', handleTime: '-' },
                        { id: 'W2023112003', trainId: 'T0912', type: '信号干扰', level: '低', time: '2023-11-20 10:15:42', location: '北京南站-陶然亭站', description: '短暂信号干扰，已自动恢复', status: '已处理', handler: '系统', handleTime: '2023-11-20 10:16:35' },
                        { id: 'W2023111908', trainId: 'T0315', type: '车门故障', level: '中', time: '2023-11-19 18:22:37', location: '惠新西街南口站', description: '3号车门关闭异常', status: '已处理', handler: '王工', handleTime: '2023-11-19 18:40:12' },
                        { id: 'W2023111805', trainId: 'T0721', type: '供电系统异常', level: '高', time: '2023-11-18 09:05:18', location: '草房站-常营站', description: '供电系统电压波动', status: '已处理', handler: '赵工', handleTime: '2023-11-18 09:30:45' }
                    ];
                }, 1000);
            },
            methods: {
                getWarningLevelClass(level) {
                    if (level === '高') return 'warning-level-high';
                    if (level === '中') return 'warning-level-medium';
                    if (level === '低') return 'warning-level-low';
                    return '';
                },
                viewWarningDetail(warning) {
                    // 查看故障详情
                    console.log('查看故障详情:', warning);
                },
                handleWarning(warning) {
                    // 处理故障
                    console.log('处理故障:', warning);
                }
            }
        }
    },
    // 添加车辆调度管理路由
    {
        path: '/dispatch',
        component: {
            template: '#dispatch-template',
            data() {
                return {
                    loading: true,
                    dispatchList: [],
                    searchQuery: '',
                    filterLine: '',
                    filterStatus: '',
                    dateValue: '',
                    detailDialogVisible: false,
                    formDialogVisible: false,
                    dialogTitle: '添加调度',
                    currentDispatch: null,
                    dispatchForm: {
                        id: '',
                        trainId: '',
                        line: '',
                        driver: '',
                        status: '待发车',
                        departureTime: '',
                        arrivalTime: '',
                        startStation: '',
                        endStation: '',
                        viaStations: [],
                        notes: '',
                        createTime: ''
                    },
                    dispatchRules: {
                        trainId: [{ required: true, message: '请选择列车', trigger: 'change' }],
                        line: [{ required: true, message: '请选择线路', trigger: 'change' }],
                        driver: [{ required: true, message: '请选择司机', trigger: 'change' }],
                        departureTime: [{ required: true, message: '请选择发车时间', trigger: 'change' }],
                        startStation: [{ required: true, message: '请选择起始站', trigger: 'change' }],
                        endStation: [{ required: true, message: '请选择终点站', trigger: 'change' }]
                    },
                    availableTrains: [
                        { value: 'T1208', label: 'T1208' },
                        { value: 'T0537', label: 'T0537' },
                        { value: 'T0912', label: 'T0912' },
                        { value: 'T0315', label: 'T0315' },
                        { value: 'T0628', label: 'T0628' }
                    ],
                    availableDrivers: [
                        { value: '张明', label: '张明' },
                        { value: '李强', label: '李强' },
                        { value: '王伟', label: '王伟' },
                        { value: '赵勇', label: '赵勇' },
                        { value: '刘洋', label: '刘洋' }
                    ],
                    stationOptions: [],
                    endStationOptions: [],
                    viaStationOptions: [],
                    stationMap: {
                        '1号线': [
                            { value: '苹果园站', label: '苹果园站' },
                            { value: '古城站', label: '古城站' },
                            { value: '八角游乐园站', label: '八角游乐园站' },
                            { value: '八宝山站', label: '八宝山站' },
                            { value: '玉泉路站', label: '玉泉路站' },
                            { value: '五棵松站', label: '五棵松站' },
                            { value: '万寿路站', label: '万寿路站' },
                            { value: '公主坟站', label: '公主坟站' },
                            { value: '军事博物馆站', label: '军事博物馆站' },
                            { value: '木樨地站', label: '木樨地站' },
                            { value: '南礼士路站', label: '南礼士路站' },
                            { value: '复兴门站', label: '复兴门站' },
                            { value: '西单站', label: '西单站' },
                            { value: '天安门西站', label: '天安门西站' },
                            { value: '天安门东站', label: '天安门东站' },
                            { value: '王府井站', label: '王府井站' },
                            { value: '东单站', label: '东单站' },
                            { value: '建国门站', label: '建国门站' },
                            { value: '永安里站', label: '永安里站' },
                            { value: '国贸站', label: '国贸站' },
                            { value: '大望路站', label: '大望路站' }
                        ],
                        '2号线': [
                            { value: '西直门站', label: '西直门站' },
                            { value: '积水潭站', label: '积水潭站' },
                            { value: '鼓楼大街站', label: '鼓楼大街站' },
                            { value: '安定门站', label: '安定门站' },
                            { value: '雍和宫站', label: '雍和宫站' },
                            { value: '东直门站', label: '东直门站' },
                            { value: '东四十条站', label: '东四十条站' },
                            { value: '朝阳门站', label: '朝阳门站' },
                            { value: '建国门站', label: '建国门站' },
                            { value: '北京站站', label: '北京站站' },
                            { value: '崇文门站', label: '崇文门站' },
                            { value: '前门站', label: '前门站' },
                            { value: '和平门站', label: '和平门站' },
                            { value: '宣武门站', label: '宣武门站' },
                            { value: '长椿街站', label: '长椿街站' },
                            { value: '复兴门站', label: '复兴门站' },
                            { value: '阜成门站', label: '阜成门站' },
                            { value: '车公庄站', label: '车公庄站' }
                        ],
                        '4号线': [
                            { value: '安河桥北站', label: '安河桥北站' },
                            { value: '北宫门站', label: '北宫门站' },
                            { value: '西苑站', label: '西苑站' },
                            { value: '圆明园站', label: '圆明园站' },
                            { value: '北京大学东门站', label: '北京大学东门站' },
                            { value: '中关村站', label: '中关村站' },
                            { value: '海淀黄庄站', label: '海淀黄庄站' },
                            { value: '人民大学站', label: '人民大学站' },
                            { value: '魏公村站', label: '魏公村站' },
                            { value: '国家图书馆站', label: '国家图书馆站' },
                            { value: '动物园站', label: '动物园站' },
                            { value: '西直门站', label: '西直门站' },
                            { value: '新街口站', label: '新街口站' },
                            { value: '平安里站', label: '平安里站' },
                            { value: '西四站', label: '西四站' },
                            { value: '灵境胡同站', label: '灵境胡同站' },
                            { value: '西单站', label: '西单站' },
                            { value: '宣武门站', label: '宣武门站' },
                            { value: '菜市口站', label: '菜市口站' },
                            { value: '陶然亭站', label: '陶然亭站' },
                            { value: '北京南站站', label: '北京南站站' }
                        ],
                        '5号线': [
                            { value: '天通苑北站', label: '天通苑北站' },
                            { value: '天通苑站', label: '天通苑站' },
                            { value: '天通苑南站', label: '天通苑南站' },
                            { value: '立水桥站', label: '立水桥站' },
                            { value: '立水桥南站', label: '立水桥南站' },
                            { value: '北苑路北站', label: '北苑路北站' },
                            { value: '大屯路东站', label: '大屯路东站' },
                            { value: '惠新西街北口站', label: '惠新西街北口站' },
                            { value: '惠新西街南口站', label: '惠新西街南口站' },
                            { value: '和平西桥站', label: '和平西桥站' },
                            { value: '和平里北街站', label: '和平里北街站' },
                            { value: '雍和宫站', label: '雍和宫站' },
                            { value: '北新桥站', label: '北新桥站' },
                            { value: '张自忠路站', label: '张自忠路站' },
                            { value: '东四站', label: '东四站' },
                            { value: '灯市口站', label: '灯市口站' },
                            { value: '东单站', label: '东单站' },
                            { value: '崇文门站', label: '崇文门站' },
                            { value: '磁器口站', label: '磁器口站' },
                            { value: '天坛东门站', label: '天坛东门站' },
                            { value: '蒲黄榆站', label: '蒲黄榆站' }
                        ],
                        '6号线': [
                            { value: '海淀五路居站', label: '海淀五路居站' },
                            { value: '慈寿寺站', label: '慈寿寺站' },
                            { value: '花园桥站', label: '花园桥站' },
                            { value: '白石桥南站', label: '白石桥南站' },
                            { value: '车公庄西站', label: '车公庄西站' },
                            { value: '车公庄站', label: '车公庄站' },
                            { value: '平安里站', label: '平安里站' },
                            { value: '北海北站', label: '北海北站' },
                            { value: '南锣鼓巷站', label: '南锣鼓巷站' },
                            { value: '东四站', label: '东四站' },
                            { value: '朝阳门站', label: '朝阳门站' },
                            { value: '东大桥站', label: '东大桥站' },
                            { value: '呼家楼站', label: '呼家楼站' },
                            { value: '金台路站', label: '金台路站' },
                            { value: '十里堡站', label: '十里堡站' },
                            { value: '青年路站', label: '青年路站' },
                            { value: '褡裢坡站', label: '褡裢坡站' },
                            { value: '黄渠站', label: '黄渠站' },
                            { value: '常营站', label: '常营站' },
                            { value: '草房站', label: '草房站' }
                        ]
                    }
                };
            },
            computed: {
                filteredDispatch() {
                    return this.dispatchList.filter(dispatch => {
                        const matchQuery = dispatch.id.toLowerCase().includes(this.searchQuery.toLowerCase()) || 
                                          dispatch.trainId.toLowerCase().includes(this.searchQuery.toLowerCase());
                        const matchLine = this.filterLine ? dispatch.line === this.filterLine : true;
                        const matchStatus = this.filterStatus ? dispatch.status === this.filterStatus : true;
                        
                        // 如果选择了日期，则过滤出当天的调度
                        let matchDate = true;
                        if (this.dateValue) {
                            const selectedDate = new Date(this.dateValue);
                            const dispatchDate = new Date(dispatch.departureTime);
                            matchDate = selectedDate.toDateString() === dispatchDate.toDateString();
                        }
                        
                        return matchQuery && matchLine && matchStatus && matchDate;
                    });
                }
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.dispatchList = [
                        { 
                            id: 'D20231122001', 
                            trainId: 'T1208', 
                            line: '1号线', 
                            driver: '张明', 
                            status: '运行中', 
                            departureTime: '2023-11-22 08:00:00', 
                            arrivalTime: '2023-11-22 09:30:00', 
                            startStation: '苹果园站', 
                            endStation: '国贸站',
                            viaStations: '古城站,八角游乐园站,八宝山站,玉泉路站,五棵松站,万寿路站,公主坟站',
                            createTime: '2023-11-21 16:30:00',
                            waitingTime: '2023-11-22 07:45:00'
                        },
                        { 
                            id: 'D20231122002', 
                            trainId: 'T0537', 
                            line: '2号线', 
                            driver: '李强', 
                            status: '待发车', 
                            departureTime: '2023-11-22 10:15:00', 
                            arrivalTime: '2023-11-22 11:45:00', 
                            startStation: '西直门站', 
                            endStation: '朝阳门站',
                            viaStations: '积水潭站,鼓楼大街站,安定门站,雍和宫站,东直门站,东四十条站',
                            createTime: '2023-11-21 17:20:00'
                        },
                        { 
                            id: 'D20231122003', 
                            trainId: 'T0912', 
                            line: '4号线', 
                            driver: '王伟', 
                            status: '已完成', 
                            departureTime: '2023-11-22 06:30:00', 
                            arrivalTime: '2023-11-22 08:00:00', 
                            startStation: '安河桥北站', 
                            endStation: '北京南站站',
                            viaStations: '北宫门站,西苑站,圆明园站,北京大学东门站,中关村站',
                            createTime: '2023-11-21 15:00:00',
                            waitingTime: '2023-11-22 06:15:00',
                            actualArrivalTime: '2023-11-22 07:58:00'
                        },
                        { 
                            id: 'D20231122004', 
                            trainId: 'T0315', 
                            line: '5号线', 
                            driver: '赵勇', 
                            status: '延误', 
                            departureTime: '2023-11-22 09:00:00', 
                            arrivalTime: '2023-11-22 10:30:00', 
                            startStation: '天通苑北站', 
                            endStation: '蒲黄榆站',
                            viaStations: '天通苑站,天通苑南站,立水桥站,立水桥南站',
                            createTime: '2023-11-21 16:00:00',
                            waitingTime: '2023-11-22 08:45:00'
                        },
                        { 
                            id: 'D20231122005', 
                            trainId: 'T0628', 
                            line: '6号线', 
                            driver: '刘洋', 
                            status: '已发车', 
                            departureTime: '2023-11-22 07:30:00', 
                            arrivalTime: '2023-11-22 09:00:00', 
                            startStation: '海淀五路居站', 
                            endStation: '草房站',
                            viaStations: '慈寿寺站,花园桥站,白石桥南站,车公庄西站',
                            createTime: '2023-11-21 14:30:00',
                            waitingTime: '2023-11-22 07:15:00'
                        }
                    ];
                }, 1000);
            },
            methods: {
                getStatusClass(status) {
                    if (status === '正常运行' || status === '运行中' || status === '已完成') return 'status-normal';
                    if (status === '减速运行' || status === '待发车' || status === '已发车') return 'status-warning';
                    if (status === '临时停车' || status === '延误' || status === '已取消') return 'status-danger';
                    return '';
                },
                viewDispatchDetail(dispatch) {
                    this.currentDispatch = JSON.parse(JSON.stringify(dispatch));
                    this.detailDialogVisible = true;
                },
                editDispatch(dispatch) {
                    this.dialogTitle = '编辑调度';
                    this.dispatchForm = JSON.parse(JSON.stringify(dispatch));
                    
                    // 将途经站点字符串转换为数组
                    if (typeof this.dispatchForm.viaStations === 'string') {
                        this.dispatchForm.viaStations = this.dispatchForm.viaStations.split(',');
                    }
                    
                    // 设置站点选项
                    this.stationOptions = this.stationMap[this.dispatchForm.line] || [];
                    this.updateEndStations();
                    this.updateViaStations();
                    
                    this.detailDialogVisible = false;
                    this.formDialogVisible = true;
                },
                addNewDispatch() {
                    this.dialogTitle = '添加调度';
                    this.dispatchForm = {
                        id: 'D' + new Date().getTime(),
                        trainId: '',
                        line: '',
                        driver: '',
                        status: '待发车',
                        departureTime: '',
                        arrivalTime: '',
                        startStation: '',
                        endStation: '',
                        viaStations: [],
                        notes: '',
                        createTime: new Date().toLocaleString()
                    };
                    this.formDialogVisible = true;
                },
                submitDispatchForm() {
                    this.$refs.dispatchForm.validate((valid) => {
                        if (valid) {
                            // 将途经站点数组转换为字符串保存
                            const formData = JSON.parse(JSON.stringify(this.dispatchForm));
                            if (Array.isArray(formData.viaStations)) {
                                formData.viaStations = formData.viaStations.join(',');
                            }
                            
                            // 查找是否已存在该ID的调度
                            const index = this.dispatchList.findIndex(item => item.id === formData.id);
                            
                            if (index !== -1) {
                                // 更新现有调度
                                this.$set(this.dispatchList, index, formData);
                                this.$message({
                                    message: '调度信息已更新',
                                    type: 'success'
                                });
                            } else {
                                // 添加新调度
                                this.dispatchList.unshift(formData);
                                this.$message({
                                    message: '调度已添加',
                                    type: 'success'
                                });
                            }
                            
                            this.formDialogVisible = false;
                        } else {
                            return false;
                        }
                    });
                },
                handleSizeChange(val) {
                    console.log(`每页 ${val} 条`);
                },
                handleCurrentChange(val) {
                    console.log(`当前页: ${val}`);
                },
                getStepActive() {
                    const statusMap = {
                        '待发车': 1,
                        '已发车': 2,
                        '运行中': 3,
                        '已完成': 4,
                        '已取消': 1,
                        '延误': 2
                    };
                    return statusMap[this.currentDispatch.status] || 0;
                },
                updateEndStations() {
                    if (!this.dispatchForm.line || !this.dispatchForm.startStation) {
                        this.endStationOptions = [];
                        return;
                    }
                    
                    // 获取当前线路的所有站点
                    const allStations = this.stationMap[this.dispatchForm.line] || [];
                    
                    // 过滤掉起始站
                    this.endStationOptions = allStations.filter(station => 
                        station.value !== this.dispatchForm.startStation
                    );
                    
                    this.updateViaStations();
                },
                updateViaStations() {
                    if (!this.dispatchForm.line || !this.dispatchForm.startStation || !this.dispatchForm.endStation) {
                        this.viaStationOptions = [];
                        return;
                    }
                    
                    // 获取当前线路的所有站点
                    const allStations = this.stationMap[this.dispatchForm.line] || [];
                    
                    // 过滤掉起始站和终点站
                    this.viaStationOptions = allStations.filter(station => 
                        station.value !== this.dispatchForm.startStation && 
                        station.value !== this.dispatchForm.endStation
                    );
                }
            },
            watch: {
                'dispatchForm.line'() {
                    // 当线路变化时，更新站点选项
                    this.stationOptions = this.stationMap[this.dispatchForm.line] || [];
                    this.dispatchForm.startStation = '';
                    this.dispatchForm.endStation = '';
                    this.dispatchForm.viaStations = [];
                    this.endStationOptions = [];
                    this.viaStationOptions = [];
                }
            }
        }
    },
    // 添加运行数据分析路由
    {
        path: '/data-analysis',
        component: {
            template: '#data-analysis-template',
            data() {
                return {
                    loading: true,
                    analysisData: {
                        dailyPassengers: [],
                        lineEfficiency: [],
                        energyConsumption: [],
                        peakHourData: []
                    },
                    timeRange: 'week',
                    selectedLine: '',
                    dateRange: ''
                };
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.loadAnalysisData();
                }, 1000);
            },
            methods: {
                loadAnalysisData() {
                    // 模拟加载分析数据
                    this.analysisData = {
                        dailyPassengers: [
                            { date: '11-16', count: 1250000, increase: 2.5 },
                            { date: '11-17', count: 1320000, increase: 5.6 },
                            { date: '11-18', count: 980000, increase: -25.8 },
                            { date: '11-19', count: 850000, increase: -13.3 },
                            { date: '11-20', count: 1380000, increase: 62.4 },
                            { date: '11-21', count: 1420000, increase: 2.9 },
                            { date: '11-22', count: 1380000, increase: -2.8 }
                        ],
                        lineEfficiency: [
                            { line: '1号线', efficiency: 92.5, punctuality: 96.8, loadFactor: 85.3 },
                            { line: '2号线', efficiency: 91.8, punctuality: 95.2, loadFactor: 87.2 },
                            { line: '4号线', efficiency: 93.2, punctuality: 97.5, loadFactor: 82.5 },
                            { line: '5号线', efficiency: 94.5, punctuality: 98.1, loadFactor: 88.7 },
                            { line: '6号线', efficiency: 90.8, punctuality: 94.7, loadFactor: 86.4 }
                        ],
                        energyConsumption: [
                            { date: '11-16', consumption: 258000, perKm: 42.5 },
                            { date: '11-17', consumption: 262000, perKm: 43.2 },
                            { date: '11-18', consumption: 245000, perKm: 40.3 },
                            { date: '11-19', consumption: 228000, perKm: 37.5 },
                            { date: '11-20', consumption: 267000, perKm: 44.1 },
                            { date: '11-21', consumption: 271000, perKm: 44.7 },
                            { date: '11-22', consumption: 265000, perKm: 43.8 }
                        ],
                        peakHourData: [
                            { hour: '7:00', passengers: 125000 },
                            { hour: '8:00', passengers: 185000 },
                            { hour: '9:00', passengers: 142000 },
                            { hour: '17:00', passengers: 158000 },
                            { hour: '18:00', passengers: 175000 },
                            { hour: '19:00', passengers: 132000 }
                        ]
                    };
                    this.initCharts();
                },
                initCharts() {
                    // 初始化客流量统计图表
                    const passengerChart = echarts.init(document.getElementById('passenger-chart'));
                    passengerChart.setOption({
                        title: {
                            text: '日客流量统计',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis',
                            formatter: '{b}<br/>客流量: {c}人次<br/>环比: {d}%'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.analysisData.dailyPassengers.map(item => item.date)
                        },
                        yAxis: {
                            type: 'value',
                            name: '人次',
                            axisLabel: {
                                formatter: value => {
                                    if (value >= 1000000) {
                                        return (value / 1000000).toFixed(1) + 'M';
                                    } else if (value >= 1000) {
                                        return (value / 1000).toFixed(0) + 'K';
                                    }
                                    return value;
                                }
                            }
                        },
                        series: [{
                            data: this.analysisData.dailyPassengers.map(item => item.count),
                            type: 'bar',
                            itemStyle: {
                                color: '#409EFF'
                            }
                        }]
                    });

                    // 初始化线路效率对比图表
                    const efficiencyChart = echarts.init(document.getElementById('efficiency-comparison-chart'));
                    efficiencyChart.setOption({
                        title: {
                            text: '线路效率对比',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        legend: {
                            data: ['运行效率', '准点率', '满载率'],
                            top: 'bottom'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.analysisData.lineEfficiency.map(item => item.line)
                        },
                        yAxis: {
                            type: 'value',
                            name: '百分比(%)',
                            min: 75
                        },
                        series: [
                            {
                                name: '运行效率',
                                type: 'bar',
                                data: this.analysisData.lineEfficiency.map(item => item.efficiency)
                            },
                            {
                                name: '准点率',
                                type: 'bar',
                                data: this.analysisData.lineEfficiency.map(item => item.punctuality)
                            },
                            {
                                name: '满载率',
                                type: 'bar',
                                data: this.analysisData.lineEfficiency.map(item => item.loadFactor)
                            }
                        ]
                    });

                    // 初始化能耗分析图表
                    const energyChart = echarts.init(document.getElementById('energy-chart'));
                    energyChart.setOption({
                        title: {
                            text: '能耗分析',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'cross'
                            }
                        },
                        legend: {
                            data: ['总能耗', '每公里能耗'],
                            top: 'bottom'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.analysisData.energyConsumption.map(item => item.date)
                        },
                        yAxis: [
                            {
                                type: 'value',
                                name: '总能耗(kWh)',
                                position: 'left'
                            },
                            {
                                type: 'value',
                                name: '每公里能耗(kWh/km)',
                                position: 'right'
                            }
                        ],
                        series: [
                            {
                                name: '总能耗',
                                type: 'bar',
                                data: this.analysisData.energyConsumption.map(item => item.consumption)
                            },
                            {
                                name: '每公里能耗',
                                type: 'line',
                                yAxisIndex: 1,
                                data: this.analysisData.energyConsumption.map(item => item.perKm)
                            }
                        ]
                    });

                    // 初始化高峰时段客流图表
                    const peakHourChart = echarts.init(document.getElementById('peak-hour-chart'));
                    peakHourChart.setOption({
                        title: {
                            text: '高峰时段客流分析',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.analysisData.peakHourData.map(item => item.hour)
                        },
                        yAxis: {
                            type: 'value',
                            name: '人次',
                            axisLabel: {
                                formatter: value => {
                                    if (value >= 1000000) {
                                        return (value / 1000000).toFixed(1) + 'M';
                                    } else if (value >= 1000) {
                                        return (value / 1000).toFixed(0) + 'K';
                                    }
                                    return value;
                                }
                            }
                        },
                        series: [{
                            data: this.analysisData.peakHourData.map(item => item.passengers),
                            type: 'line',
                            smooth: true,
                            areaStyle: {},
                            itemStyle: {
                                color: '#67C23A'
                            }
                        }]
                    });

                    // 监听窗口大小变化，重新调整图表大小
                    window.addEventListener('resize', function() {
                        passengerChart.resize();
                        efficiencyChart.resize();
                        energyChart.resize();
                        peakHourChart.resize();
                    });
                },
                changeTimeRange(range) {
                    this.timeRange = range;
                    this.loading = true;
                    // 模拟数据加载
                    setTimeout(() => {
                        this.loading = false;
                        this.loadAnalysisData();
                    }, 500);
                },
                exportData(type) {
                    // 导出数据
                    console.log('导出数据:', type);
                }
            }
        }
    },
    // 添加安全评估路由
    {
        path: '/safety',
        component: {
            template: '#safety-template',
            data() {
                return {
                    loading: true,
                    selectedPeriod: 'week',
                    activeTab: 'equipment',
                    safetyScore: 0,
                    safetyIndicators: [],
                    equipmentSafetyScore: 0,
                    operationSafetyScore: 0,
                    personnelSafetyScore: 0,
                    equipmentSafetyData: [],
                    operationSafetyData: [],
                    personnelSafetyData: [],
                    safetyIncidents: [],
                    riskWarnings: [],
                    safetyTrends: [],
                    incidentStatistics: {
                        totalIncidents: 0,
                        resolvedIncidents: 0,
                        pendingIncidents: 0,
                        avgResolutionTime: 0,
                        incidentsByType: {},
                        incidentsByLevel: {},
                        monthlyTrend: []
                    },
                    incidentDialogVisible: false,
                    incidentFormDialogVisible: false,
                    incidentDialogTitle: '添加安全事件',
                    currentIncident: null,
                    incidentForm: {
                        id: '',
                        time: '',
                        type: '',
                        level: '',
                        location: '',
                        description: '',
                        impact: '',
                        measures: '',
                        status: '待处理',
                        responsible: '',
                        recorder: ''
                    },
                    incidentRules: {
                        time: [{ required: true, message: '请选择发生时间', trigger: 'change' }],
                        type: [{ required: true, message: '请选择事件类型', trigger: 'change' }],
                        level: [{ required: true, message: '请选择事件级别', trigger: 'change' }],
                        location: [{ required: true, message: '请输入发生位置', trigger: 'blur' }],
                        description: [{ required: true, message: '请输入事件描述', trigger: 'blur' }]
                    }
                };
            },
            mounted() {
                this.loadSafetyData();
                // 监听窗口大小变化
                window.addEventListener('resize', this.resizeCharts);
            },
            beforeDestroy() {
                // 组件销毁前停止风险监测
                this.stopRiskMonitoring();
                // 移除窗口大小变化监听器
                window.removeEventListener('resize', this.resizeCharts);
            },
            methods: {
                loadSafetyData() {
                    this.loading = true;
                    // 模拟数据加载
                    setTimeout(() => {
                        this.loading = false;
                        this.initSafetyData();
                        this.calculateSafetyScore();
                        this.calculateIncidentStatistics(); // 计算事件统计
                        this.initSafetyCharts();
                        this.startRiskMonitoring(); // 启动风险监测
                    }, 1000);
                },
                initSafetyData() {
                    // 初始化安全指标数据
                    this.safetyIndicators = [
                        { title: '设备安全', score: 94, trend: 'up' },
                        { title: '运行安全', score: 96, trend: 'up' },
                        { title: '人员安全', score: 92, trend: 'down' },
                        { title: '环境安全', score: 98, trend: 'stable' },
                        { title: '信息安全', score: 89, trend: 'up' },
                        { title: '应急响应', score: 91, trend: 'stable' }
                    ];

                    // 设备安全数据
                    this.equipmentSafetyData = [
                        { category: '信号系统', checkItems: 45, passRate: 97.8, score: 95, issues: '2个轻微故障已修复', status: '正常' },
                        { category: '动力系统', checkItems: 38, passRate: 94.7, score: 92, issues: '1个中等故障处理中', status: '注意' },
                        { category: '制动系统', checkItems: 42, passRate: 98.1, score: 96, issues: '无异常', status: '正常' },
                        { category: '车门系统', checkItems: 28, passRate: 96.4, score: 94, issues: '定期维护中', status: '正常' },
                        { category: '空调系统', checkItems: 35, passRate: 95.2, score: 93, issues: '1个轻微故障', status: '正常' },
                        { category: '监控系统', checkItems: 52, passRate: 99.0, score: 98, issues: '运行良好', status: '优秀' }
                    ];

                    // 运行安全数据
                    this.operationSafetyData = [
                        { category: '运行速度', standard: '≤80km/h', actual: '76.5km/h', deviation: -4.4, score: 96, suggestion: '保持当前运行状态' },
                        { category: '制动距离', standard: '≤150m', actual: '142m', deviation: -5.3, score: 95, suggestion: '制动性能良好' },
                        { category: '信号响应', standard: '≤2s', actual: '1.8s', deviation: -10.0, score: 98, suggestion: '响应时间优秀' },
                        { category: '停车精度', standard: '±0.5m', actual: '±0.3m', deviation: -40.0, score: 97, suggestion: '停车精度很高' },
                        { category: '发车间隔', standard: '120s', actual: '118s', deviation: -1.7, score: 94, suggestion: '间隔控制良好' },
                        { category: '准点率', standard: '≥95%', actual: '96.8%', deviation: 1.9, score: 96, suggestion: '准点率达标' }
                    ];

                    // 人员安全数据
                    this.personnelSafetyData = [
                        { category: '安全培训', standard: '每月不少于8小时', completion: 95, score: 95, notes: '本月已完成7.6小时' },
                        { category: '健康检查', standard: '每季度体检', completion: 88, score: 88, notes: '2人延期体检' },
                        { category: '应急演练', standard: '每月1次', completion: 100, score: 100, notes: '本月已完成演练' },
                        { category: '违规记录', standard: '0起', completion: 92, score: 92, notes: '本月2起轻微违规' },
                        { category: '资质认证', standard: '100%持证上岗', completion: 98, score: 98, notes: '1人证书即将到期' },
                        { category: '安全意识', standard: '考核≥90分', completion: 94, score: 94, notes: '平均分93.2分' }
                    ];

                    // 安全事件数据
                    this.safetyIncidents = [
                        {
                            id: 'SI2023112201',
                            time: '2023-11-22 14:30:00',
                            type: '设备故障',
                            level: '中',
                            location: '1号线西单站',
                            description: '车门传感器异常导致车门无法正常关闭',
                            impact: '列车延误5分钟，影响后续3班列车',
                            measures: '更换传感器，调整运行计划',
                            status: '已处理',
                            handler: '张工',
                            handleTime: '2023-11-22 15:15:00'
                        },
                        {
                            id: 'SI2023112105',
                            time: '2023-11-21 09:15:00',
                            type: '人员违规',
                            level: '低',
                            location: '2号线调度室',
                            description: '司机未按规定进行发车前检查',
                            impact: '无直接影响，存在安全隐患',
                            measures: '对司机进行安全教育，加强监督',
                            status: '已处理',
                            handler: '李主管',
                            handleTime: '2023-11-21 10:30:00'
                        },
                        {
                            id: 'SI2023112003',
                            time: '2023-11-20 16:45:00',
                            type: '环境异常',
                            level: '高',
                            location: '4号线隧道区间',
                            description: '隧道内发现积水，可能影响设备安全',
                            impact: '该区间限速运行，运能下降20%',
                            measures: '排水处理，设备检查，恢复正常运行',
                            status: '已处理',
                            handler: '王工程师',
                            handleTime: '2023-11-20 19:30:00'
                        }
                    ];

                    // 风险预警数据
                    this.riskWarnings = [
                        { type: '设备老化', level: '中', count: 3, trend: 'stable', description: '部分设备接近维护周期' },
                        { type: '人员疲劳', level: '低', count: 1, trend: 'down', description: '夜班司机疲劳度监测' },
                        { type: '天气影响', level: '中', count: 2, trend: 'up', description: '近期雨雪天气增多' },
                        { type: '客流高峰', level: '高', count: 1, trend: 'up', description: '节假日客流压力大' }
                    ];

                    // 设置各项评分
                    this.equipmentSafetyScore = 94;
                    this.operationSafetyScore = 96;
                    this.personnelSafetyScore = 92;
                },
                calculateSafetyScore() {
                    // 增强的安全评分算法
                    const weights = {
                        equipment: 0.3,  // 设备安全权重30%
                        operation: 0.35, // 运行安全权重35%
                        personnel: 0.25, // 人员安全权重25%
                        environment: 0.1 // 环境安全权重10%
                    };

                    // 计算设备安全评分
                    this.equipmentSafetyScore = this.calculateEquipmentScore();

                    // 计算运行安全评分
                    this.operationSafetyScore = this.calculateOperationScore();

                    // 计算人员安全评分
                    this.personnelSafetyScore = this.calculatePersonnelScore();

                    // 环境安全评分（基于天气、客流等因素）
                    const environmentScore = this.calculateEnvironmentScore();

                    // 计算综合评分
                    let baseScore =
                        this.equipmentSafetyScore * weights.equipment +
                        this.operationSafetyScore * weights.operation +
                        this.personnelSafetyScore * weights.personnel +
                        environmentScore * weights.environment;

                    // 应用风险调整因子
                    const riskAdjustment = this.calculateRiskAdjustment();

                    // 应用历史事件影响因子
                    const historyAdjustment = this.calculateHistoryAdjustment();

                    this.safetyScore = Math.max(0, Math.min(100, Math.round(
                        baseScore * riskAdjustment * historyAdjustment
                    )));
                },
                calculateEquipmentScore() {
                    // 基于设备检查数据计算设备安全评分
                    let totalScore = 0;
                    let totalWeight = 0;

                    this.equipmentSafetyData.forEach(item => {
                        const weight = item.checkItems; // 检查项数量作为权重
                        totalScore += item.score * weight;
                        totalWeight += weight;
                    });

                    return totalWeight > 0 ? Math.round(totalScore / totalWeight) : 0;
                },
                calculateOperationScore() {
                    // 基于运行数据计算运行安全评分
                    let totalScore = 0;
                    let count = 0;

                    this.operationSafetyData.forEach(item => {
                        // 根据偏差率调整评分
                        let adjustedScore = item.score;
                        if (Math.abs(item.deviation) > 10) {
                            adjustedScore *= 0.9; // 偏差大于10%时降低评分
                        } else if (Math.abs(item.deviation) > 5) {
                            adjustedScore *= 0.95; // 偏差大于5%时轻微降低评分
                        }

                        totalScore += adjustedScore;
                        count++;
                    });

                    return count > 0 ? Math.round(totalScore / count) : 0;
                },
                calculatePersonnelScore() {
                    // 基于人员安全数据计算人员安全评分
                    let totalScore = 0;
                    let count = 0;

                    this.personnelSafetyData.forEach(item => {
                        // 根据完成情况调整评分
                        let adjustedScore = item.score * (item.completion / 100);
                        totalScore += adjustedScore;
                        count++;
                    });

                    return count > 0 ? Math.round(totalScore / count) : 0;
                },
                calculateEnvironmentScore() {
                    // 计算环境安全评分（基于天气、客流、时间等因素）
                    let baseScore = 95;

                    // 天气因素调整
                    const weatherCondition = this.getCurrentWeatherCondition();
                    if (weatherCondition === 'severe') {
                        baseScore -= 10;
                    } else if (weatherCondition === 'moderate') {
                        baseScore -= 5;
                    }

                    // 客流因素调整
                    const passengerLoad = this.getCurrentPassengerLoad();
                    if (passengerLoad > 90) {
                        baseScore -= 8;
                    } else if (passengerLoad > 80) {
                        baseScore -= 4;
                    }

                    // 时间因素调整（高峰期风险较高）
                    const isPeakHour = this.isPeakHour();
                    if (isPeakHour) {
                        baseScore -= 3;
                    }

                    return Math.max(70, baseScore);
                },
                calculateRiskAdjustment() {
                    // 基于当前风险预警计算调整因子
                    let adjustment = 1.0;

                    this.riskWarnings.forEach(risk => {
                        if (risk.level === '高') {
                            adjustment *= 0.95; // 高风险降低5%
                        } else if (risk.level === '中') {
                            adjustment *= 0.98; // 中风险降低2%
                        }
                    });

                    return adjustment;
                },
                calculateHistoryAdjustment() {
                    // 基于近期安全事件计算调整因子
                    let adjustment = 1.0;
                    const recentIncidents = this.safetyIncidents.filter(incident => {
                        const incidentDate = new Date(incident.time);
                        const daysDiff = (new Date() - incidentDate) / (1000 * 60 * 60 * 24);
                        return daysDiff <= 7; // 近7天的事件
                    });

                    recentIncidents.forEach(incident => {
                        if (incident.level === '高') {
                            adjustment *= 0.92; // 高级事件降低8%
                        } else if (incident.level === '中') {
                            adjustment *= 0.96; // 中级事件降低4%
                        } else if (incident.level === '低') {
                            adjustment *= 0.98; // 低级事件降低2%
                        }
                    });

                    return adjustment;
                },
                getCurrentWeatherCondition() {
                    // 模拟获取当前天气状况
                    const conditions = ['good', 'moderate', 'severe'];
                    const weights = [0.7, 0.25, 0.05]; // 好天气概率70%，一般25%，恶劣5%
                    const random = Math.random();
                    let cumulative = 0;

                    for (let i = 0; i < conditions.length; i++) {
                        cumulative += weights[i];
                        if (random <= cumulative) {
                            return conditions[i];
                        }
                    }
                    return 'good';
                },
                getCurrentPassengerLoad() {
                    // 模拟获取当前客流负载率
                    return Math.floor(Math.random() * 40) + 60; // 60-100%之间
                },
                isPeakHour() {
                    // 判断是否为高峰时段
                    const hour = new Date().getHours();
                    return (hour >= 7 && hour <= 9) || (hour >= 17 && hour <= 19);
                },
                startRiskMonitoring() {
                    // 启动风险实时监测
                    this.riskMonitoringInterval = setInterval(() => {
                        this.performRiskAssessment();
                    }, 30000); // 每30秒检查一次
                },
                stopRiskMonitoring() {
                    // 停止风险监测
                    if (this.riskMonitoringInterval) {
                        clearInterval(this.riskMonitoringInterval);
                        this.riskMonitoringInterval = null;
                    }
                },
                performRiskAssessment() {
                    // 执行风险评估
                    const currentRisks = this.identifyCurrentRisks();

                    // 检查是否有新的高风险
                    currentRisks.forEach(risk => {
                        if (risk.level === '高' && !this.isRiskAlreadyWarned(risk)) {
                            this.triggerRiskAlert(risk);
                        }
                    });

                    // 更新风险预警数据
                    this.updateRiskWarnings(currentRisks);
                },
                identifyCurrentRisks() {
                    // 识别当前风险
                    const risks = [];

                    // 检查设备风险
                    const equipmentRisks = this.assessEquipmentRisks();
                    risks.push(...equipmentRisks);

                    // 检查运行风险
                    const operationRisks = this.assessOperationRisks();
                    risks.push(...operationRisks);

                    // 检查人员风险
                    const personnelRisks = this.assessPersonnelRisks();
                    risks.push(...personnelRisks);

                    // 检查环境风险
                    const environmentRisks = this.assessEnvironmentRisks();
                    risks.push(...environmentRisks);

                    return risks;
                },
                assessEquipmentRisks() {
                    // 评估设备风险
                    const risks = [];

                    this.equipmentSafetyData.forEach(equipment => {
                        if (equipment.passRate < 90) {
                            risks.push({
                                type: '设备故障风险',
                                level: equipment.passRate < 80 ? '高' : '中',
                                source: equipment.category,
                                description: `${equipment.category}通过率仅为${equipment.passRate}%`,
                                suggestion: '建议立即进行设备检修和维护',
                                timestamp: new Date().toISOString()
                            });
                        }
                    });

                    return risks;
                },
                assessOperationRisks() {
                    // 评估运行风险
                    const risks = [];

                    this.operationSafetyData.forEach(operation => {
                        if (Math.abs(operation.deviation) > 15) {
                            risks.push({
                                type: '运行异常风险',
                                level: Math.abs(operation.deviation) > 25 ? '高' : '中',
                                source: operation.category,
                                description: `${operation.category}偏差率达到${operation.deviation}%`,
                                suggestion: operation.suggestion || '建议检查运行参数并调整',
                                timestamp: new Date().toISOString()
                            });
                        }
                    });

                    return risks;
                },
                assessPersonnelRisks() {
                    // 评估人员风险
                    const risks = [];

                    this.personnelSafetyData.forEach(personnel => {
                        if (personnel.completion < 80) {
                            risks.push({
                                type: '人员安全风险',
                                level: personnel.completion < 60 ? '高' : '中',
                                source: personnel.category,
                                description: `${personnel.category}完成率仅为${personnel.completion}%`,
                                suggestion: '建议加强人员培训和管理',
                                timestamp: new Date().toISOString()
                            });
                        }
                    });

                    return risks;
                },
                assessEnvironmentRisks() {
                    // 评估环境风险
                    const risks = [];

                    // 检查天气风险
                    const weather = this.getCurrentWeatherCondition();
                    if (weather === 'severe') {
                        risks.push({
                            type: '恶劣天气风险',
                            level: '高',
                            source: '天气监测',
                            description: '当前天气条件恶劣，可能影响列车安全运行',
                            suggestion: '建议降低运行速度，增加安全检查频次',
                            timestamp: new Date().toISOString()
                        });
                    }

                    // 检查客流风险
                    const passengerLoad = this.getCurrentPassengerLoad();
                    if (passengerLoad > 95) {
                        risks.push({
                            type: '客流过载风险',
                            level: '中',
                            source: '客流监测',
                            description: `当前客流负载率达到${passengerLoad}%`,
                            suggestion: '建议增加列车班次，疏导客流',
                            timestamp: new Date().toISOString()
                        });
                    }

                    return risks;
                },
                isRiskAlreadyWarned(risk) {
                    // 检查风险是否已经预警过
                    return this.warnedRisks && this.warnedRisks.some(warned =>
                        warned.type === risk.type &&
                        warned.source === risk.source &&
                        (new Date() - new Date(warned.timestamp)) < 300000 // 5分钟内不重复预警
                    );
                },
                triggerRiskAlert(risk) {
                    // 触发风险预警
                    this.$notify({
                        title: '安全风险预警',
                        message: `${risk.type}: ${risk.description}`,
                        type: 'warning',
                        duration: 0, // 不自动关闭
                        position: 'top-right'
                    });

                    // 记录已预警的风险
                    if (!this.warnedRisks) {
                        this.warnedRisks = [];
                    }
                    this.warnedRisks.push(risk);

                    // 可以在这里添加其他预警机制，如发送短信、邮件等
                    console.log('风险预警触发:', risk);
                },
                updateRiskWarnings(currentRisks) {
                    // 更新风险预警显示
                    const riskSummary = this.summarizeRisks(currentRisks);
                    this.riskWarnings = riskSummary;
                },
                summarizeRisks(risks) {
                    // 汇总风险信息
                    const summary = {};

                    risks.forEach(risk => {
                        const key = risk.type;
                        if (!summary[key]) {
                            summary[key] = {
                                type: key,
                                level: risk.level,
                                count: 0,
                                trend: 'stable',
                                description: risk.description
                            };
                        }
                        summary[key].count++;

                        // 取最高风险等级
                        if (risk.level === '高' || (risk.level === '中' && summary[key].level === '低')) {
                            summary[key].level = risk.level;
                        }
                    });

                    return Object.values(summary);
                },
                calculateIncidentStatistics() {
                    // 计算安全事件统计数据
                    const stats = {
                        totalIncidents: this.safetyIncidents.length,
                        resolvedIncidents: 0,
                        pendingIncidents: 0,
                        avgResolutionTime: 0,
                        incidentsByType: {},
                        incidentsByLevel: {},
                        monthlyTrend: []
                    };

                    let totalResolutionTime = 0;
                    let resolvedCount = 0;

                    this.safetyIncidents.forEach(incident => {
                        // 统计处理状态
                        if (incident.status === '已处理' || incident.status === '已关闭') {
                            stats.resolvedIncidents++;

                            // 计算处理时间
                            if (incident.handleTime && incident.time) {
                                const startTime = new Date(incident.time);
                                const endTime = new Date(incident.handleTime);
                                const resolutionTime = (endTime - startTime) / (1000 * 60 * 60); // 小时
                                totalResolutionTime += resolutionTime;
                                resolvedCount++;
                            }
                        } else {
                            stats.pendingIncidents++;
                        }

                        // 按类型统计
                        if (!stats.incidentsByType[incident.type]) {
                            stats.incidentsByType[incident.type] = 0;
                        }
                        stats.incidentsByType[incident.type]++;

                        // 按级别统计
                        if (!stats.incidentsByLevel[incident.level]) {
                            stats.incidentsByLevel[incident.level] = 0;
                        }
                        stats.incidentsByLevel[incident.level]++;
                    });

                    // 计算平均处理时间
                    stats.avgResolutionTime = resolvedCount > 0 ?
                        Math.round(totalResolutionTime / resolvedCount * 10) / 10 : 0;

                    // 生成月度趋势数据
                    stats.monthlyTrend = this.generateMonthlyTrend();

                    this.incidentStatistics = stats;
                },
                generateMonthlyTrend() {
                    // 生成近6个月的事件趋势数据
                    const months = [];
                    const now = new Date();

                    for (let i = 5; i >= 0; i--) {
                        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
                        const monthName = date.toLocaleDateString('zh-CN', { month: 'short' });

                        // 模拟月度事件数量（实际应用中从数据库查询）
                        const count = Math.floor(Math.random() * 10) + 2;

                        months.push({
                            month: monthName,
                            count: count,
                            resolved: Math.floor(count * 0.8),
                            pending: Math.floor(count * 0.2)
                        });
                    }

                    return months;
                },
                analyzeIncidentTrends() {
                    // 分析事件趋势
                    const analysis = {
                        riskLevel: 'low',
                        trendDirection: 'stable',
                        recommendations: []
                    };

                    // 分析事件数量趋势
                    const recentMonths = this.incidentStatistics.monthlyTrend.slice(-3);
                    const avgRecent = recentMonths.reduce((sum, month) => sum + month.count, 0) / 3;
                    const previousMonths = this.incidentStatistics.monthlyTrend.slice(-6, -3);
                    const avgPrevious = previousMonths.reduce((sum, month) => sum + month.count, 0) / 3;

                    if (avgRecent > avgPrevious * 1.2) {
                        analysis.trendDirection = 'increasing';
                        analysis.riskLevel = 'high';
                        analysis.recommendations.push('事件数量呈上升趋势，建议加强安全管理');
                    } else if (avgRecent < avgPrevious * 0.8) {
                        analysis.trendDirection = 'decreasing';
                        analysis.recommendations.push('事件数量呈下降趋势，安全管理效果良好');
                    }

                    // 分析处理效率
                    const resolutionRate = this.incidentStatistics.resolvedIncidents / this.incidentStatistics.totalIncidents;
                    if (resolutionRate < 0.8) {
                        analysis.riskLevel = 'medium';
                        analysis.recommendations.push('事件处理效率偏低，建议优化处理流程');
                    }

                    // 分析平均处理时间
                    if (this.incidentStatistics.avgResolutionTime > 24) {
                        analysis.recommendations.push('平均处理时间较长，建议提高响应速度');
                    }

                    return analysis;
                },
                generateIncidentReport() {
                    // 生成安全事件报告
                    const analysis = this.analyzeIncidentTrends();
                    const report = {
                        reportDate: new Date().toLocaleDateString('zh-CN'),
                        statistics: this.incidentStatistics,
                        analysis: analysis,
                        summary: this.generateReportSummary()
                    };

                    // 这里可以调用后端API保存报告或导出文件
                    console.log('安全事件报告:', report);

                    this.$message({
                        message: '安全事件报告已生成',
                        type: 'success'
                    });

                    return report;
                },
                generateReportSummary() {
                    // 生成报告摘要
                    const stats = this.incidentStatistics;
                    const resolutionRate = Math.round(stats.resolvedIncidents / stats.totalIncidents * 100);

                    return `本期共发生安全事件${stats.totalIncidents}起，已处理${stats.resolvedIncidents}起，处理率${resolutionRate}%。平均处理时间${stats.avgResolutionTime}小时。主要事件类型为${Object.keys(stats.incidentsByType)[0]}。`;
                },
                refreshSafetyData() {
                    this.loadSafetyData();
                    this.$message({
                        message: '安全评估数据已刷新',
                        type: 'success'
                    });
                },
                getScoreColor() {
                    if (this.safetyScore >= 95) return '#67C23A';
                    if (this.safetyScore >= 85) return '#E6A23C';
                    return '#F56C6C';
                },
                getScoreDesc() {
                    if (this.safetyScore >= 95) return '安全状况优秀';
                    if (this.safetyScore >= 90) return '安全状况良好';
                    if (this.safetyScore >= 85) return '安全状况一般';
                    if (this.safetyScore >= 80) return '存在安全隐患';
                    return '安全状况较差';
                },
                getIndicatorClass(score) {
                    if (score >= 95) return 'indicator-excellent';
                    if (score >= 90) return 'indicator-good';
                    if (score >= 85) return 'indicator-normal';
                    if (score >= 80) return 'indicator-warning';
                    return 'indicator-danger';
                },
                getIndicatorColor(score) {
                    if (score >= 95) return '#67C23A';
                    if (score >= 90) return '#95D475';
                    if (score >= 85) return '#E6A23C';
                    if (score >= 80) return '#F78989';
                    return '#F56C6C';
                },
                getStatusType(status) {
                    const typeMap = {
                        '优秀': 'success',
                        '正常': 'success',
                        '注意': 'warning',
                        '异常': 'danger'
                    };
                    return typeMap[status] || 'info';
                },
                getDeviationClass(deviation) {
                    if (Math.abs(deviation) <= 5) return 'deviation-normal';
                    if (Math.abs(deviation) <= 10) return 'deviation-warning';
                    return 'deviation-danger';
                },
                getIncidentLevelType(level) {
                    const typeMap = {
                        '高': 'danger',
                        '中': 'warning',
                        '低': 'info'
                    };
                    return typeMap[level] || 'info';
                },
                addSafetyIncident() {
                    this.incidentDialogTitle = '添加安全事件';
                    this.incidentForm = {
                        id: 'SI' + new Date().getTime(),
                        time: '',
                        type: '',
                        level: '',
                        location: '',
                        description: '',
                        impact: '',
                        measures: '',
                        status: '待处理',
                        responsible: '',
                        recorder: '当前用户'
                    };
                    this.incidentFormDialogVisible = true;
                },
                submitIncidentForm() {
                    this.$refs.incidentForm.validate((valid) => {
                        if (valid) {
                            const formData = {
                                ...this.incidentForm,
                                handler: '当前用户',
                                handleTime: new Date().toLocaleString(),
                                reportTime: new Date().toLocaleString()
                            };

                            // 查找是否已存在该ID的事件
                            const index = this.safetyIncidents.findIndex(item => item.id === formData.id);

                            if (index !== -1) {
                                // 更新现有事件
                                this.$set(this.safetyIncidents, index, formData);
                                this.$message({
                                    message: '安全事件已更新',
                                    type: 'success'
                                });
                            } else {
                                // 添加新事件
                                this.safetyIncidents.unshift(formData);
                                this.$message({
                                    message: '安全事件已添加',
                                    type: 'success'
                                });
                            }

                            this.incidentFormDialogVisible = false;
                        }
                    });
                },
                viewIncidentDetail(incident) {
                    this.$alert(
                        `<div style="text-align: left;">
                            <p><strong>事件ID：</strong>${incident.id}</p>
                            <p><strong>发生时间：</strong>${incident.time}</p>
                            <p><strong>事件类型：</strong>${incident.type}</p>
                            <p><strong>事件级别：</strong>${incident.level}</p>
                            <p><strong>发生位置：</strong>${incident.location}</p>
                            <p><strong>事件描述：</strong>${incident.description}</p>
                            <p><strong>影响范围：</strong>${incident.impact}</p>
                            <p><strong>处理措施：</strong>${incident.measures}</p>
                            <p><strong>处理状态：</strong>${incident.status}</p>
                            <p><strong>处理人员：</strong>${incident.handler}</p>
                            <p><strong>处理时间：</strong>${incident.handleTime}</p>
                        </div>`,
                        '安全事件详情',
                        {
                            dangerouslyUseHTMLString: true,
                            confirmButtonText: '确定'
                        }
                    );
                },
                editIncident(incident) {
                    this.incidentDialogTitle = '编辑安全事件';
                    this.incidentForm = JSON.parse(JSON.stringify(incident));
                    this.incidentDialogVisible = false;
                    this.incidentFormDialogVisible = true;
                },
                getIncidentStatusType(status) {
                    const typeMap = {
                        '待处理': 'warning',
                        '处理中': 'primary',
                        '已处理': 'success',
                        '已关闭': 'info'
                    };
                    return typeMap[status] || 'info';
                },
                getIncidentStepActive() {
                    if (!this.currentIncident) return 0;
                    const statusMap = {
                        '待处理': 1,
                        '处理中': 2,
                        '已处理': 3,
                        '已关闭': 4
                    };
                    return statusMap[this.currentIncident.status] || 0;
                },
                handleSizeChange(val) {
                    console.log(`每页 ${val} 条`);
                },
                handleCurrentChange(val) {
                    console.log(`当前页: ${val}`);
                },
                getRiskLevelType(level) {
                    const typeMap = {
                        '高': 'danger',
                        '中': 'warning',
                        '低': 'info'
                    };
                    return typeMap[level] || 'info';
                },
                getRiskTrendIcon(trend) {
                    const iconMap = {
                        'up': 'el-icon-top',
                        'down': 'el-icon-bottom',
                        'stable': 'el-icon-minus'
                    };
                    return iconMap[trend] || 'el-icon-minus';
                },
                getRiskTrendColor(trend) {
                    const colorMap = {
                        'up': '#F56C6C',
                        'down': '#67C23A',
                        'stable': '#909399'
                    };
                    return colorMap[trend] || '#909399';
                },
                getRiskTrendText(trend) {
                    const textMap = {
                        'up': '上升',
                        'down': '下降',
                        'stable': '稳定'
                    };
                    return textMap[trend] || '稳定';
                },
                exportSafetyReport() {
                    // 生成安全评估报告
                    const report = this.generateSafetyReport();

                    // 创建下载链接
                    const dataStr = JSON.stringify(report, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);

                    // 创建下载元素
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `安全评估报告_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.json`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    // 释放URL对象
                    URL.revokeObjectURL(url);

                    this.$message({
                        message: '安全评估报告已导出',
                        type: 'success'
                    });
                },
                generateSafetyReport() {
                    // 生成完整的安全评估报告
                    return {
                        reportInfo: {
                            title: '轨道交通运输列车运行安全评估报告',
                            generateTime: new Date().toLocaleString('zh-CN'),
                            period: this.selectedPeriod,
                            version: '1.0'
                        },
                        safetyScore: {
                            overall: this.safetyScore,
                            equipment: this.equipmentSafetyScore,
                            operation: this.operationSafetyScore,
                            personnel: this.personnelSafetyScore,
                            indicators: this.safetyIndicators
                        },
                        riskAssessment: {
                            currentRisks: this.riskWarnings,
                            riskAnalysis: this.analyzeIncidentTrends()
                        },
                        incidentAnalysis: {
                            statistics: this.incidentStatistics,
                            incidents: this.safetyIncidents,
                            summary: this.generateReportSummary()
                        },
                        recommendations: this.generateRecommendations(),
                        appendix: {
                            equipmentData: this.equipmentSafetyData,
                            operationData: this.operationSafetyData,
                            personnelData: this.personnelSafetyData
                        }
                    };
                },
                generateRecommendations() {
                    // 生成改进建议
                    const recommendations = [];

                    // 基于安全评分生成建议
                    if (this.safetyScore < 90) {
                        recommendations.push({
                            category: '综合安全',
                            priority: 'high',
                            description: '综合安全评分偏低，建议全面检查安全管理体系',
                            actions: ['加强安全培训', '完善安全制度', '增加安全检查频次']
                        });
                    }

                    // 基于设备安全生成建议
                    if (this.equipmentSafetyScore < 95) {
                        recommendations.push({
                            category: '设备安全',
                            priority: 'medium',
                            description: '设备安全评分有待提升',
                            actions: ['加强设备维护', '更新老旧设备', '完善设备监控系统']
                        });
                    }

                    // 基于风险预警生成建议
                    this.riskWarnings.forEach(risk => {
                        if (risk.level === '高') {
                            recommendations.push({
                                category: '风险管控',
                                priority: 'high',
                                description: `${risk.type}风险等级较高`,
                                actions: ['立即制定应对措施', '加强监控', '准备应急预案']
                            });
                        }
                    });

                    return recommendations;
                },
                initSafetyCharts() {
                    // 初始化安全趋势图表
                    this.initSafetyTrendChart();

                    // 初始化事件类型分布图表
                    this.initIncidentTypeChart();

                    // 初始化风险等级分布图表
                    this.initRiskLevelChart();

                    // 初始化月度事件趋势图表
                    this.initMonthlyIncidentChart();
                },
                initSafetyTrendChart() {
                    if (document.getElementById('safety-trend-chart')) {
                        const trendChart = echarts.init(document.getElementById('safety-trend-chart'));
                        trendChart.setOption({
                            tooltip: {
                                trigger: 'axis'
                            },
                            legend: {
                                data: ['综合评分', '设备安全', '运行安全', '人员安全'],
                                top: 'bottom'
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: ['11-16', '11-17', '11-18', '11-19', '11-20', '11-21', '11-22']
                            },
                            yAxis: {
                                type: 'value',
                                min: 80,
                                max: 100
                            },
                            series: [
                                {
                                    name: '综合评分',
                                    type: 'line',
                                    data: [92, 94, 93, 95, 94, 96, 95],
                                    itemStyle: { color: '#409EFF' },
                                    smooth: true
                                },
                                {
                                    name: '设备安全',
                                    type: 'line',
                                    data: [91, 93, 92, 94, 93, 95, 94],
                                    itemStyle: { color: '#67C23A' },
                                    smooth: true
                                },
                                {
                                    name: '运行安全',
                                    type: 'line',
                                    data: [94, 96, 95, 97, 96, 98, 96],
                                    itemStyle: { color: '#E6A23C' },
                                    smooth: true
                                },
                                {
                                    name: '人员安全',
                                    type: 'line',
                                    data: [90, 92, 91, 93, 92, 94, 92],
                                    itemStyle: { color: '#F56C6C' },
                                    smooth: true
                                }
                            ]
                        });

                        this.safetyCharts = this.safetyCharts || [];
                        this.safetyCharts.push(trendChart);
                    }
                },
                initIncidentTypeChart() {
                    if (document.getElementById('incident-type-chart')) {
                        const typeChart = echarts.init(document.getElementById('incident-type-chart'));
                        const typeData = Object.entries(this.incidentStatistics.incidentsByType).map(([type, count]) => ({
                            name: type,
                            value: count
                        }));

                        typeChart.setOption({
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b}: {c} ({d}%)'
                            },
                            legend: {
                                orient: 'vertical',
                                left: 'left',
                                data: typeData.map(item => item.name)
                            },
                            series: [
                                {
                                    name: '事件类型',
                                    type: 'pie',
                                    radius: '60%',
                                    center: ['60%', '50%'],
                                    data: typeData,
                                    emphasis: {
                                        itemStyle: {
                                            shadowBlur: 10,
                                            shadowOffsetX: 0,
                                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                                        }
                                    }
                                }
                            ]
                        });

                        this.safetyCharts = this.safetyCharts || [];
                        this.safetyCharts.push(typeChart);
                    }
                },
                initRiskLevelChart() {
                    if (document.getElementById('risk-level-chart')) {
                        const riskChart = echarts.init(document.getElementById('risk-level-chart'));

                        riskChart.setOption({
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: ['设备风险', '运行风险', '人员风险', '环境风险']
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: [
                                {
                                    name: '高风险',
                                    type: 'bar',
                                    stack: 'total',
                                    data: [1, 0, 0, 1],
                                    itemStyle: { color: '#F56C6C' }
                                },
                                {
                                    name: '中风险',
                                    type: 'bar',
                                    stack: 'total',
                                    data: [2, 1, 1, 1],
                                    itemStyle: { color: '#E6A23C' }
                                },
                                {
                                    name: '低风险',
                                    type: 'bar',
                                    stack: 'total',
                                    data: [1, 2, 1, 0],
                                    itemStyle: { color: '#409EFF' }
                                }
                            ]
                        });

                        this.safetyCharts = this.safetyCharts || [];
                        this.safetyCharts.push(riskChart);
                    }
                },
                initMonthlyIncidentChart() {
                    if (document.getElementById('monthly-incident-chart')) {
                        const monthlyChart = echarts.init(document.getElementById('monthly-incident-chart'));
                        const monthlyData = this.incidentStatistics.monthlyTrend;

                        monthlyChart.setOption({
                            tooltip: {
                                trigger: 'axis'
                            },
                            legend: {
                                data: ['总事件', '已处理', '待处理'],
                                top: 'bottom'
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: monthlyData.map(item => item.month)
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: [
                                {
                                    name: '总事件',
                                    type: 'line',
                                    data: monthlyData.map(item => item.count),
                                    itemStyle: { color: '#409EFF' }
                                },
                                {
                                    name: '已处理',
                                    type: 'bar',
                                    data: monthlyData.map(item => item.resolved),
                                    itemStyle: { color: '#67C23A' }
                                },
                                {
                                    name: '待处理',
                                    type: 'bar',
                                    data: monthlyData.map(item => item.pending),
                                    itemStyle: { color: '#F56C6C' }
                                }
                            ]
                        });

                        this.safetyCharts = this.safetyCharts || [];
                        this.safetyCharts.push(monthlyChart);
                    }
                },
                resizeCharts() {
                    // 调整所有图表大小
                    if (this.safetyCharts) {
                        this.safetyCharts.forEach(chart => {
                            if (chart && chart.resize) {
                                chart.resize();
                            }
                        });
                    }
                }
            },
            watch: {
                selectedPeriod(newPeriod) {
                    this.loadSafetyData();
                }
            }
        }
    },
    // 其他路由配置...
];

// 创建路由实例
const router = new VueRouter({
    routes
});

// 创建Vue实例
new Vue({
    el: '#app',
    router,
    data: {
        activeMenu: '/',
        currentPage: '监测总览',
        currentTime: '',
        sidebarCollapsed: false
    },
    created() {
        this.updateTime();
        // 每秒更新一次时间
        setInterval(this.updateTime, 1000);
    },
    watch: {
        '$route'(to) {
            this.activeMenu = to.path;
            switch(to.path) {
                case '/':
                    this.currentPage = '监测总览';
                    break;
                case '/train-status':
                    this.currentPage = '列车运行状态';
                    break;
                case '/train-track':
                    this.currentPage = '列车运行轨迹';
                    break;
                case '/warning':
                    this.currentPage = '故障预警';
                    break;
                case '/dispatch':
                    this.currentPage = '车辆调度管理';
                    break;
                case '/data-analysis':
                    this.currentPage = '运行数据分析';
                    break;
                case '/safety':
                    this.currentPage = '安全评估';
                    break;
                case '/statistics':
                    this.currentPage = '运营数据统计';
                    break;
                case '/settings':
                    this.currentPage = '系统设置';
                    break;
            }
        }
    },
    methods: {
        updateTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            this.currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        toggleSidebar() {
            this.sidebarCollapsed = !this.sidebarCollapsed;
            // 切换侧边栏宽度
            const aside = document.querySelector('.el-aside');
            if (aside) {
                aside.style.width = this.sidebarCollapsed ? '64px' : '200px';
            }
        }
    }
}); 