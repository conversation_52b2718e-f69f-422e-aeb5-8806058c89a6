// 路由配置
const routes = [
    {
        path: '/',
        component: {
            template: '#dashboard-template',
            data() {
                return {
                    loading: true,
                    statistics: {
                        totalTrains: 0,
                        runningTrains: 0,
                        warningCount: 0,
                        safetyScore: 0
                    },
                    recentWarnings: [],
                    trainStatusData: []
                };
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.statistics = {
                        totalTrains: 128,
                        runningTrains: 87,
                        warningCount: 3,
                        safetyScore: 96.8
                    };
                    this.recentWarnings = [
                        { id: 'W2023112201', trainId: 'T1208', type: '温度异常', level: '中', time: '2023-11-22 08:32:15', status: '已处理' },
                        { id: 'W2023112105', trainId: 'T0537', type: '制动系统异常', level: '高', time: '2023-11-21 15:47:23', status: '处理中' },
                        { id: 'W2023112003', trainId: 'T0912', type: '信号干扰', level: '低', time: '2023-11-20 10:15:42', status: '已处理' }
                    ];
                    this.trainStatusData = [
                        { trainId: 'T1208', line: '1号线', status: '正常运行', speed: '60km/h', position: '西单站-复兴门站', nextStation: '复兴门站', arrivalTime: '2分钟' },
                        { trainId: 'T0537', line: '2号线', status: '减速运行', speed: '45km/h', position: '朝阳门站-东四十条站', nextStation: '东四十条站', arrivalTime: '4分钟' },
                        { trainId: 'T0912', line: '4号线', status: '正常运行', speed: '58km/h', position: '北京南站-陶然亭站', nextStation: '陶然亭站', arrivalTime: '3分钟' }
                    ];
                    this.initCharts();
                }, 1000);
            },
            methods: {
                initCharts() {
                    // 初始化列车运行状态统计图表
                    const statusChart = echarts.init(document.getElementById('train-status-chart'));
                    statusChart.setOption({
                        title: {
                            text: '列车运行状态分布',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c} ({d}%)'
                        },
                        legend: {
                            orient: 'vertical',
                            left: 'left',
                            data: ['正常运行', '减速运行', '临时停车', '检修中', '未运行']
                        },
                        series: [
                            {
                                name: '运行状态',
                                type: 'pie',
                                radius: '60%',
                                center: ['50%', '50%'],
                                data: [
                                    { value: 78, name: '正常运行' },
                                    { value: 9, name: '减速运行' },
                                    { value: 3, name: '临时停车' },
                                    { value: 12, name: '检修中' },
                                    { value: 26, name: '未运行' }
                                ],
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                }
                            }
                        ]
                    });

                    // 初始化故障类型统计图表
                    const warningChart = echarts.init(document.getElementById('warning-chart'));
                    warningChart.setOption({
                        title: {
                            text: '近30天故障类型统计',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        legend: {
                            data: ['低级故障', '中级故障', '高级故障'],
                            top: 'bottom'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: ['信号系统', '动力系统', '制动系统', '车门系统', '空调系统', '监控系统', '其他']
                        },
                        yAxis: {
                            type: 'value'
                        },
                        series: [
                            {
                                name: '低级故障',
                                type: 'bar',
                                stack: 'total',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: [12, 8, 5, 7, 9, 3, 4]
                            },
                            {
                                name: '中级故障',
                                type: 'bar',
                                stack: 'total',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: [5, 4, 3, 2, 3, 1, 2]
                            },
                            {
                                name: '高级故障',
                                type: 'bar',
                                stack: 'total',
                                emphasis: {
                                    focus: 'series'
                                },
                                data: [1, 2, 3, 0, 1, 0, 1]
                            }
                        ]
                    });

                    // 初始化运行效率统计图表
                    const efficiencyChart = echarts.init(document.getElementById('efficiency-chart'));
                    efficiencyChart.setOption({
                        title: {
                            text: '近7天运行效率统计',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        legend: {
                            data: ['准点率', '满载率', '运行效率'],
                            top: 'bottom'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            boundaryGap: false,
                            data: ['11-16', '11-17', '11-18', '11-19', '11-20', '11-21', '11-22']
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                formatter: '{value}%'
                            }
                        },
                        series: [
                            {
                                name: '准点率',
                                type: 'line',
                                data: [98.2, 97.8, 98.5, 99.1, 98.7, 97.9, 98.4]
                            },
                            {
                                name: '满载率',
                                type: 'line',
                                data: [85.3, 87.2, 86.5, 88.7, 90.1, 89.5, 88.2]
                            },
                            {
                                name: '运行效率',
                                type: 'line',
                                data: [92.5, 91.8, 93.2, 94.5, 93.8, 92.7, 93.5]
                            }
                        ]
                    });

                    // 监听窗口大小变化，重新调整图表大小
                    window.addEventListener('resize', function() {
                        statusChart.resize();
                        warningChart.resize();
                        efficiencyChart.resize();
                    });
                },
                getStatusClass(status) {
                    if (status === '正常运行') return 'status-normal';
                    if (status === '减速运行') return 'status-warning';
                    if (status === '临时停车') return 'status-danger';
                    return '';
                },
                getWarningLevelClass(level) {
                    if (level === '高') return 'warning-level-high';
                    if (level === '中') return 'warning-level-medium';
                    if (level === '低') return 'warning-level-low';
                    return '';
                }
            }
        }
    },
    {
        path: '/train-status',
        component: {
            template: '#train-status-template',
            data() {
                return {
                    loading: true,
                    trainList: [],
                    searchQuery: '',
                    filterLine: '',
                    filterStatus: ''
                };
            },
            computed: {
                filteredTrains() {
                    return this.trainList.filter(train => {
                        const matchQuery = train.trainId.toLowerCase().includes(this.searchQuery.toLowerCase()) || 
                                          train.line.toLowerCase().includes(this.searchQuery.toLowerCase());
                        const matchLine = this.filterLine ? train.line === this.filterLine : true;
                        const matchStatus = this.filterStatus ? train.status === this.filterStatus : true;
                        return matchQuery && matchLine && matchStatus;
                    });
                }
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.trainList = [
                        { trainId: 'T1208', line: '1号线', status: '正常运行', speed: '60km/h', position: '西单站-复兴门站', nextStation: '复兴门站', arrivalTime: '2分钟', temperature: '23°C', humidity: '45%', passengers: '中等' },
                        { trainId: 'T0537', line: '2号线', status: '减速运行', speed: '45km/h', position: '朝阳门站-东四十条站', nextStation: '东四十条站', arrivalTime: '4分钟', temperature: '24°C', humidity: '48%', passengers: '较多' },
                        { trainId: 'T0912', line: '4号线', status: '正常运行', speed: '58km/h', position: '北京南站-陶然亭站', nextStation: '陶然亭站', arrivalTime: '3分钟', temperature: '22°C', humidity: '42%', passengers: '较少' },
                        { trainId: 'T0315', line: '5号线', status: '正常运行', speed: '62km/h', position: '惠新西街南口站-和平西桥站', nextStation: '和平西桥站', arrivalTime: '2分钟', temperature: '23°C', humidity: '44%', passengers: '中等' },
                        { trainId: 'T0721', line: '6号线', status: '临时停车', speed: '0km/h', position: '草房站', nextStation: '常营站', arrivalTime: '未知', temperature: '25°C', humidity: '50%', passengers: '较多' }
                    ];
                    this.initMap();
                }, 1000);
            },
            methods: {
                initMap() {
                    // 模拟地图初始化，实际项目中可能使用百度地图、高德地图等API
                    console.log('初始化列车运行地图');
                },
                getStatusClass(status) {
                    if (status === '正常运行') return 'status-normal';
                    if (status === '减速运行') return 'status-warning';
                    if (status === '临时停车') return 'status-danger';
                    return '';
                },
                viewTrainDetail(train) {
                    // 查看列车详情
                    console.log('查看列车详情:', train);
                }
            }
        }
    },
    {
        path: '/train-track',
        component: {
            template: '#train-track-template',
            data() {
                return {
                    loading: true,
                    selectedTrain: '',
                    trainOptions: [],
                    trackData: []
                };
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.trainOptions = [
                        { value: 'T1208', label: 'T1208 (1号线)' },
                        { value: 'T0537', label: 'T0537 (2号线)' },
                        { value: 'T0912', label: 'T0912 (4号线)' },
                        { value: 'T0315', label: 'T0315 (5号线)' },
                        { value: 'T0721', label: 'T0721 (6号线)' }
                    ];
                }, 1000);
            },
            methods: {
                loadTrackData() {
                    if (!this.selectedTrain) return;
                    
                    this.loading = true;
                    // 模拟加载轨迹数据
                    setTimeout(() => {
                        this.loading = false;
                        this.trackData = [
                            { time: '08:00:00', station: '西直门站', status: '正常停靠', speed: '0km/h', passengers: '上车45人，下车32人' },
                            { time: '08:02:30', station: '西直门站-大钟寺站', status: '正常运行', speed: '60km/h', passengers: '约180人' },
                            { time: '08:05:00', station: '大钟寺站', status: '正常停靠', speed: '0km/h', passengers: '上车28人，下车15人' },
                            { time: '08:07:30', station: '大钟寺站-知春路站', status: '正常运行', speed: '58km/h', passengers: '约195人' },
                            { time: '08:10:00', station: '知春路站', status: '正常停靠', speed: '0km/h', passengers: '上车35人，下车22人' },
                            { time: '08:12:30', station: '知春路站-五道口站', status: '正常运行', speed: '62km/h', passengers: '约210人' },
                            { time: '08:15:00', station: '五道口站', status: '正常停靠', speed: '0km/h', passengers: '上车42人，下车38人' }
                        ];
                        this.initTrackMap();
                    }, 1000);
                },
                initTrackMap() {
                    // 模拟轨迹地图初始化
                    console.log('初始化列车轨迹地图');
                }
            }
        }
    },
    {
        path: '/warning',
        component: {
            template: '#warning-template',
            data() {
                return {
                    loading: true,
                    warningList: [],
                    searchQuery: '',
                    filterLevel: '',
                    filterStatus: '',
                    dateRange: ''
                };
            },
            computed: {
                filteredWarnings() {
                    return this.warningList.filter(warning => {
                        const matchQuery = warning.id.toLowerCase().includes(this.searchQuery.toLowerCase()) || 
                                          warning.trainId.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                                          warning.type.toLowerCase().includes(this.searchQuery.toLowerCase());
                        const matchLevel = this.filterLevel ? warning.level === this.filterLevel : true;
                        const matchStatus = this.filterStatus ? warning.status === this.filterStatus : true;
                        return matchQuery && matchLevel && matchStatus;
                    });
                }
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.warningList = [
                        { id: 'W2023112201', trainId: 'T1208', type: '温度异常', level: '中', time: '2023-11-22 08:32:15', location: '西单站-复兴门站', description: '电机温度超过正常范围', status: '已处理', handler: '张工', handleTime: '2023-11-22 09:15:22' },
                        { id: 'W2023112105', trainId: 'T0537', type: '制动系统异常', level: '高', time: '2023-11-21 15:47:23', location: '朝阳门站-东四十条站', description: '制动系统压力异常波动', status: '处理中', handler: '李工', handleTime: '-' },
                        { id: 'W2023112003', trainId: 'T0912', type: '信号干扰', level: '低', time: '2023-11-20 10:15:42', location: '北京南站-陶然亭站', description: '短暂信号干扰，已自动恢复', status: '已处理', handler: '系统', handleTime: '2023-11-20 10:16:35' },
                        { id: 'W2023111908', trainId: 'T0315', type: '车门故障', level: '中', time: '2023-11-19 18:22:37', location: '惠新西街南口站', description: '3号车门关闭异常', status: '已处理', handler: '王工', handleTime: '2023-11-19 18:40:12' },
                        { id: 'W2023111805', trainId: 'T0721', type: '供电系统异常', level: '高', time: '2023-11-18 09:05:18', location: '草房站-常营站', description: '供电系统电压波动', status: '已处理', handler: '赵工', handleTime: '2023-11-18 09:30:45' }
                    ];
                }, 1000);
            },
            methods: {
                getWarningLevelClass(level) {
                    if (level === '高') return 'warning-level-high';
                    if (level === '中') return 'warning-level-medium';
                    if (level === '低') return 'warning-level-low';
                    return '';
                },
                viewWarningDetail(warning) {
                    // 查看故障详情
                    console.log('查看故障详情:', warning);
                },
                handleWarning(warning) {
                    // 处理故障
                    console.log('处理故障:', warning);
                }
            }
        }
    },
    // 添加车辆调度管理路由
    {
        path: '/dispatch',
        component: {
            template: '#dispatch-template',
            data() {
                return {
                    loading: true,
                    dispatchList: [],
                    searchQuery: '',
                    filterLine: '',
                    filterStatus: '',
                    dateValue: '',
                    detailDialogVisible: false,
                    formDialogVisible: false,
                    dialogTitle: '添加调度',
                    currentDispatch: null,
                    dispatchForm: {
                        id: '',
                        trainId: '',
                        line: '',
                        driver: '',
                        status: '待发车',
                        departureTime: '',
                        arrivalTime: '',
                        startStation: '',
                        endStation: '',
                        viaStations: [],
                        notes: '',
                        createTime: ''
                    },
                    dispatchRules: {
                        trainId: [{ required: true, message: '请选择列车', trigger: 'change' }],
                        line: [{ required: true, message: '请选择线路', trigger: 'change' }],
                        driver: [{ required: true, message: '请选择司机', trigger: 'change' }],
                        departureTime: [{ required: true, message: '请选择发车时间', trigger: 'change' }],
                        startStation: [{ required: true, message: '请选择起始站', trigger: 'change' }],
                        endStation: [{ required: true, message: '请选择终点站', trigger: 'change' }]
                    },
                    availableTrains: [
                        { value: 'T1208', label: 'T1208' },
                        { value: 'T0537', label: 'T0537' },
                        { value: 'T0912', label: 'T0912' },
                        { value: 'T0315', label: 'T0315' },
                        { value: 'T0628', label: 'T0628' }
                    ],
                    availableDrivers: [
                        { value: '张明', label: '张明' },
                        { value: '李强', label: '李强' },
                        { value: '王伟', label: '王伟' },
                        { value: '赵勇', label: '赵勇' },
                        { value: '刘洋', label: '刘洋' }
                    ],
                    stationOptions: [],
                    endStationOptions: [],
                    viaStationOptions: [],
                    stationMap: {
                        '1号线': [
                            { value: '苹果园站', label: '苹果园站' },
                            { value: '古城站', label: '古城站' },
                            { value: '八角游乐园站', label: '八角游乐园站' },
                            { value: '八宝山站', label: '八宝山站' },
                            { value: '玉泉路站', label: '玉泉路站' },
                            { value: '五棵松站', label: '五棵松站' },
                            { value: '万寿路站', label: '万寿路站' },
                            { value: '公主坟站', label: '公主坟站' },
                            { value: '军事博物馆站', label: '军事博物馆站' },
                            { value: '木樨地站', label: '木樨地站' },
                            { value: '南礼士路站', label: '南礼士路站' },
                            { value: '复兴门站', label: '复兴门站' },
                            { value: '西单站', label: '西单站' },
                            { value: '天安门西站', label: '天安门西站' },
                            { value: '天安门东站', label: '天安门东站' },
                            { value: '王府井站', label: '王府井站' },
                            { value: '东单站', label: '东单站' },
                            { value: '建国门站', label: '建国门站' },
                            { value: '永安里站', label: '永安里站' },
                            { value: '国贸站', label: '国贸站' },
                            { value: '大望路站', label: '大望路站' }
                        ],
                        '2号线': [
                            { value: '西直门站', label: '西直门站' },
                            { value: '积水潭站', label: '积水潭站' },
                            { value: '鼓楼大街站', label: '鼓楼大街站' },
                            { value: '安定门站', label: '安定门站' },
                            { value: '雍和宫站', label: '雍和宫站' },
                            { value: '东直门站', label: '东直门站' },
                            { value: '东四十条站', label: '东四十条站' },
                            { value: '朝阳门站', label: '朝阳门站' },
                            { value: '建国门站', label: '建国门站' },
                            { value: '北京站站', label: '北京站站' },
                            { value: '崇文门站', label: '崇文门站' },
                            { value: '前门站', label: '前门站' },
                            { value: '和平门站', label: '和平门站' },
                            { value: '宣武门站', label: '宣武门站' },
                            { value: '长椿街站', label: '长椿街站' },
                            { value: '复兴门站', label: '复兴门站' },
                            { value: '阜成门站', label: '阜成门站' },
                            { value: '车公庄站', label: '车公庄站' }
                        ],
                        '4号线': [
                            { value: '安河桥北站', label: '安河桥北站' },
                            { value: '北宫门站', label: '北宫门站' },
                            { value: '西苑站', label: '西苑站' },
                            { value: '圆明园站', label: '圆明园站' },
                            { value: '北京大学东门站', label: '北京大学东门站' },
                            { value: '中关村站', label: '中关村站' },
                            { value: '海淀黄庄站', label: '海淀黄庄站' },
                            { value: '人民大学站', label: '人民大学站' },
                            { value: '魏公村站', label: '魏公村站' },
                            { value: '国家图书馆站', label: '国家图书馆站' },
                            { value: '动物园站', label: '动物园站' },
                            { value: '西直门站', label: '西直门站' },
                            { value: '新街口站', label: '新街口站' },
                            { value: '平安里站', label: '平安里站' },
                            { value: '西四站', label: '西四站' },
                            { value: '灵境胡同站', label: '灵境胡同站' },
                            { value: '西单站', label: '西单站' },
                            { value: '宣武门站', label: '宣武门站' },
                            { value: '菜市口站', label: '菜市口站' },
                            { value: '陶然亭站', label: '陶然亭站' },
                            { value: '北京南站站', label: '北京南站站' }
                        ],
                        '5号线': [
                            { value: '天通苑北站', label: '天通苑北站' },
                            { value: '天通苑站', label: '天通苑站' },
                            { value: '天通苑南站', label: '天通苑南站' },
                            { value: '立水桥站', label: '立水桥站' },
                            { value: '立水桥南站', label: '立水桥南站' },
                            { value: '北苑路北站', label: '北苑路北站' },
                            { value: '大屯路东站', label: '大屯路东站' },
                            { value: '惠新西街北口站', label: '惠新西街北口站' },
                            { value: '惠新西街南口站', label: '惠新西街南口站' },
                            { value: '和平西桥站', label: '和平西桥站' },
                            { value: '和平里北街站', label: '和平里北街站' },
                            { value: '雍和宫站', label: '雍和宫站' },
                            { value: '北新桥站', label: '北新桥站' },
                            { value: '张自忠路站', label: '张自忠路站' },
                            { value: '东四站', label: '东四站' },
                            { value: '灯市口站', label: '灯市口站' },
                            { value: '东单站', label: '东单站' },
                            { value: '崇文门站', label: '崇文门站' },
                            { value: '磁器口站', label: '磁器口站' },
                            { value: '天坛东门站', label: '天坛东门站' },
                            { value: '蒲黄榆站', label: '蒲黄榆站' }
                        ],
                        '6号线': [
                            { value: '海淀五路居站', label: '海淀五路居站' },
                            { value: '慈寿寺站', label: '慈寿寺站' },
                            { value: '花园桥站', label: '花园桥站' },
                            { value: '白石桥南站', label: '白石桥南站' },
                            { value: '车公庄西站', label: '车公庄西站' },
                            { value: '车公庄站', label: '车公庄站' },
                            { value: '平安里站', label: '平安里站' },
                            { value: '北海北站', label: '北海北站' },
                            { value: '南锣鼓巷站', label: '南锣鼓巷站' },
                            { value: '东四站', label: '东四站' },
                            { value: '朝阳门站', label: '朝阳门站' },
                            { value: '东大桥站', label: '东大桥站' },
                            { value: '呼家楼站', label: '呼家楼站' },
                            { value: '金台路站', label: '金台路站' },
                            { value: '十里堡站', label: '十里堡站' },
                            { value: '青年路站', label: '青年路站' },
                            { value: '褡裢坡站', label: '褡裢坡站' },
                            { value: '黄渠站', label: '黄渠站' },
                            { value: '常营站', label: '常营站' },
                            { value: '草房站', label: '草房站' }
                        ]
                    }
                };
            },
            computed: {
                filteredDispatch() {
                    return this.dispatchList.filter(dispatch => {
                        const matchQuery = dispatch.id.toLowerCase().includes(this.searchQuery.toLowerCase()) || 
                                          dispatch.trainId.toLowerCase().includes(this.searchQuery.toLowerCase());
                        const matchLine = this.filterLine ? dispatch.line === this.filterLine : true;
                        const matchStatus = this.filterStatus ? dispatch.status === this.filterStatus : true;
                        
                        // 如果选择了日期，则过滤出当天的调度
                        let matchDate = true;
                        if (this.dateValue) {
                            const selectedDate = new Date(this.dateValue);
                            const dispatchDate = new Date(dispatch.departureTime);
                            matchDate = selectedDate.toDateString() === dispatchDate.toDateString();
                        }
                        
                        return matchQuery && matchLine && matchStatus && matchDate;
                    });
                }
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.dispatchList = [
                        { 
                            id: 'D20231122001', 
                            trainId: 'T1208', 
                            line: '1号线', 
                            driver: '张明', 
                            status: '运行中', 
                            departureTime: '2023-11-22 08:00:00', 
                            arrivalTime: '2023-11-22 09:30:00', 
                            startStation: '苹果园站', 
                            endStation: '国贸站',
                            viaStations: '古城站,八角游乐园站,八宝山站,玉泉路站,五棵松站,万寿路站,公主坟站',
                            createTime: '2023-11-21 16:30:00',
                            waitingTime: '2023-11-22 07:45:00'
                        },
                        { 
                            id: 'D20231122002', 
                            trainId: 'T0537', 
                            line: '2号线', 
                            driver: '李强', 
                            status: '待发车', 
                            departureTime: '2023-11-22 10:15:00', 
                            arrivalTime: '2023-11-22 11:45:00', 
                            startStation: '西直门站', 
                            endStation: '朝阳门站',
                            viaStations: '积水潭站,鼓楼大街站,安定门站,雍和宫站,东直门站,东四十条站',
                            createTime: '2023-11-21 17:20:00'
                        },
                        { 
                            id: 'D20231122003', 
                            trainId: 'T0912', 
                            line: '4号线', 
                            driver: '王伟', 
                            status: '已完成', 
                            departureTime: '2023-11-22 06:30:00', 
                            arrivalTime: '2023-11-22 08:00:00', 
                            startStation: '安河桥北站', 
                            endStation: '北京南站站',
                            viaStations: '北宫门站,西苑站,圆明园站,北京大学东门站,中关村站',
                            createTime: '2023-11-21 15:00:00',
                            waitingTime: '2023-11-22 06:15:00',
                            actualArrivalTime: '2023-11-22 07:58:00'
                        },
                        { 
                            id: 'D20231122004', 
                            trainId: 'T0315', 
                            line: '5号线', 
                            driver: '赵勇', 
                            status: '延误', 
                            departureTime: '2023-11-22 09:00:00', 
                            arrivalTime: '2023-11-22 10:30:00', 
                            startStation: '天通苑北站', 
                            endStation: '蒲黄榆站',
                            viaStations: '天通苑站,天通苑南站,立水桥站,立水桥南站',
                            createTime: '2023-11-21 16:00:00',
                            waitingTime: '2023-11-22 08:45:00'
                        },
                        { 
                            id: 'D20231122005', 
                            trainId: 'T0628', 
                            line: '6号线', 
                            driver: '刘洋', 
                            status: '已发车', 
                            departureTime: '2023-11-22 07:30:00', 
                            arrivalTime: '2023-11-22 09:00:00', 
                            startStation: '海淀五路居站', 
                            endStation: '草房站',
                            viaStations: '慈寿寺站,花园桥站,白石桥南站,车公庄西站',
                            createTime: '2023-11-21 14:30:00',
                            waitingTime: '2023-11-22 07:15:00'
                        }
                    ];
                }, 1000);
            },
            methods: {
                getStatusClass(status) {
                    if (status === '正常运行' || status === '运行中' || status === '已完成') return 'status-normal';
                    if (status === '减速运行' || status === '待发车' || status === '已发车') return 'status-warning';
                    if (status === '临时停车' || status === '延误' || status === '已取消') return 'status-danger';
                    return '';
                },
                viewDispatchDetail(dispatch) {
                    this.currentDispatch = JSON.parse(JSON.stringify(dispatch));
                    this.detailDialogVisible = true;
                },
                editDispatch(dispatch) {
                    this.dialogTitle = '编辑调度';
                    this.dispatchForm = JSON.parse(JSON.stringify(dispatch));
                    
                    // 将途经站点字符串转换为数组
                    if (typeof this.dispatchForm.viaStations === 'string') {
                        this.dispatchForm.viaStations = this.dispatchForm.viaStations.split(',');
                    }
                    
                    // 设置站点选项
                    this.stationOptions = this.stationMap[this.dispatchForm.line] || [];
                    this.updateEndStations();
                    this.updateViaStations();
                    
                    this.detailDialogVisible = false;
                    this.formDialogVisible = true;
                },
                addNewDispatch() {
                    this.dialogTitle = '添加调度';
                    this.dispatchForm = {
                        id: 'D' + new Date().getTime(),
                        trainId: '',
                        line: '',
                        driver: '',
                        status: '待发车',
                        departureTime: '',
                        arrivalTime: '',
                        startStation: '',
                        endStation: '',
                        viaStations: [],
                        notes: '',
                        createTime: new Date().toLocaleString()
                    };
                    this.formDialogVisible = true;
                },
                submitDispatchForm() {
                    this.$refs.dispatchForm.validate((valid) => {
                        if (valid) {
                            // 将途经站点数组转换为字符串保存
                            const formData = JSON.parse(JSON.stringify(this.dispatchForm));
                            if (Array.isArray(formData.viaStations)) {
                                formData.viaStations = formData.viaStations.join(',');
                            }
                            
                            // 查找是否已存在该ID的调度
                            const index = this.dispatchList.findIndex(item => item.id === formData.id);
                            
                            if (index !== -1) {
                                // 更新现有调度
                                this.$set(this.dispatchList, index, formData);
                                this.$message({
                                    message: '调度信息已更新',
                                    type: 'success'
                                });
                            } else {
                                // 添加新调度
                                this.dispatchList.unshift(formData);
                                this.$message({
                                    message: '调度已添加',
                                    type: 'success'
                                });
                            }
                            
                            this.formDialogVisible = false;
                        } else {
                            return false;
                        }
                    });
                },
                handleSizeChange(val) {
                    console.log(`每页 ${val} 条`);
                },
                handleCurrentChange(val) {
                    console.log(`当前页: ${val}`);
                },
                getStepActive() {
                    const statusMap = {
                        '待发车': 1,
                        '已发车': 2,
                        '运行中': 3,
                        '已完成': 4,
                        '已取消': 1,
                        '延误': 2
                    };
                    return statusMap[this.currentDispatch.status] || 0;
                },
                updateEndStations() {
                    if (!this.dispatchForm.line || !this.dispatchForm.startStation) {
                        this.endStationOptions = [];
                        return;
                    }
                    
                    // 获取当前线路的所有站点
                    const allStations = this.stationMap[this.dispatchForm.line] || [];
                    
                    // 过滤掉起始站
                    this.endStationOptions = allStations.filter(station => 
                        station.value !== this.dispatchForm.startStation
                    );
                    
                    this.updateViaStations();
                },
                updateViaStations() {
                    if (!this.dispatchForm.line || !this.dispatchForm.startStation || !this.dispatchForm.endStation) {
                        this.viaStationOptions = [];
                        return;
                    }
                    
                    // 获取当前线路的所有站点
                    const allStations = this.stationMap[this.dispatchForm.line] || [];
                    
                    // 过滤掉起始站和终点站
                    this.viaStationOptions = allStations.filter(station => 
                        station.value !== this.dispatchForm.startStation && 
                        station.value !== this.dispatchForm.endStation
                    );
                }
            },
            watch: {
                'dispatchForm.line'() {
                    // 当线路变化时，更新站点选项
                    this.stationOptions = this.stationMap[this.dispatchForm.line] || [];
                    this.dispatchForm.startStation = '';
                    this.dispatchForm.endStation = '';
                    this.dispatchForm.viaStations = [];
                    this.endStationOptions = [];
                    this.viaStationOptions = [];
                }
            }
        }
    },
    // 添加运行数据分析路由
    {
        path: '/data-analysis',
        component: {
            template: '#data-analysis-template',
            data() {
                return {
                    loading: true,
                    analysisData: {
                        dailyPassengers: [],
                        lineEfficiency: [],
                        energyConsumption: [],
                        peakHourData: []
                    },
                    timeRange: 'week',
                    selectedLine: '',
                    dateRange: ''
                };
            },
            mounted() {
                // 模拟数据加载
                setTimeout(() => {
                    this.loading = false;
                    this.loadAnalysisData();
                }, 1000);
            },
            methods: {
                loadAnalysisData() {
                    // 模拟加载分析数据
                    this.analysisData = {
                        dailyPassengers: [
                            { date: '11-16', count: 1250000, increase: 2.5 },
                            { date: '11-17', count: 1320000, increase: 5.6 },
                            { date: '11-18', count: 980000, increase: -25.8 },
                            { date: '11-19', count: 850000, increase: -13.3 },
                            { date: '11-20', count: 1380000, increase: 62.4 },
                            { date: '11-21', count: 1420000, increase: 2.9 },
                            { date: '11-22', count: 1380000, increase: -2.8 }
                        ],
                        lineEfficiency: [
                            { line: '1号线', efficiency: 92.5, punctuality: 96.8, loadFactor: 85.3 },
                            { line: '2号线', efficiency: 91.8, punctuality: 95.2, loadFactor: 87.2 },
                            { line: '4号线', efficiency: 93.2, punctuality: 97.5, loadFactor: 82.5 },
                            { line: '5号线', efficiency: 94.5, punctuality: 98.1, loadFactor: 88.7 },
                            { line: '6号线', efficiency: 90.8, punctuality: 94.7, loadFactor: 86.4 }
                        ],
                        energyConsumption: [
                            { date: '11-16', consumption: 258000, perKm: 42.5 },
                            { date: '11-17', consumption: 262000, perKm: 43.2 },
                            { date: '11-18', consumption: 245000, perKm: 40.3 },
                            { date: '11-19', consumption: 228000, perKm: 37.5 },
                            { date: '11-20', consumption: 267000, perKm: 44.1 },
                            { date: '11-21', consumption: 271000, perKm: 44.7 },
                            { date: '11-22', consumption: 265000, perKm: 43.8 }
                        ],
                        peakHourData: [
                            { hour: '7:00', passengers: 125000 },
                            { hour: '8:00', passengers: 185000 },
                            { hour: '9:00', passengers: 142000 },
                            { hour: '17:00', passengers: 158000 },
                            { hour: '18:00', passengers: 175000 },
                            { hour: '19:00', passengers: 132000 }
                        ]
                    };
                    this.initCharts();
                },
                initCharts() {
                    // 初始化客流量统计图表
                    const passengerChart = echarts.init(document.getElementById('passenger-chart'));
                    passengerChart.setOption({
                        title: {
                            text: '日客流量统计',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis',
                            formatter: '{b}<br/>客流量: {c}人次<br/>环比: {d}%'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.analysisData.dailyPassengers.map(item => item.date)
                        },
                        yAxis: {
                            type: 'value',
                            name: '人次',
                            axisLabel: {
                                formatter: value => {
                                    if (value >= 1000000) {
                                        return (value / 1000000).toFixed(1) + 'M';
                                    } else if (value >= 1000) {
                                        return (value / 1000).toFixed(0) + 'K';
                                    }
                                    return value;
                                }
                            }
                        },
                        series: [{
                            data: this.analysisData.dailyPassengers.map(item => item.count),
                            type: 'bar',
                            itemStyle: {
                                color: '#409EFF'
                            }
                        }]
                    });

                    // 初始化线路效率对比图表
                    const efficiencyChart = echarts.init(document.getElementById('efficiency-comparison-chart'));
                    efficiencyChart.setOption({
                        title: {
                            text: '线路效率对比',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        legend: {
                            data: ['运行效率', '准点率', '满载率'],
                            top: 'bottom'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.analysisData.lineEfficiency.map(item => item.line)
                        },
                        yAxis: {
                            type: 'value',
                            name: '百分比(%)',
                            min: 75
                        },
                        series: [
                            {
                                name: '运行效率',
                                type: 'bar',
                                data: this.analysisData.lineEfficiency.map(item => item.efficiency)
                            },
                            {
                                name: '准点率',
                                type: 'bar',
                                data: this.analysisData.lineEfficiency.map(item => item.punctuality)
                            },
                            {
                                name: '满载率',
                                type: 'bar',
                                data: this.analysisData.lineEfficiency.map(item => item.loadFactor)
                            }
                        ]
                    });

                    // 初始化能耗分析图表
                    const energyChart = echarts.init(document.getElementById('energy-chart'));
                    energyChart.setOption({
                        title: {
                            text: '能耗分析',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'cross'
                            }
                        },
                        legend: {
                            data: ['总能耗', '每公里能耗'],
                            top: 'bottom'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '15%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.analysisData.energyConsumption.map(item => item.date)
                        },
                        yAxis: [
                            {
                                type: 'value',
                                name: '总能耗(kWh)',
                                position: 'left'
                            },
                            {
                                type: 'value',
                                name: '每公里能耗(kWh/km)',
                                position: 'right'
                            }
                        ],
                        series: [
                            {
                                name: '总能耗',
                                type: 'bar',
                                data: this.analysisData.energyConsumption.map(item => item.consumption)
                            },
                            {
                                name: '每公里能耗',
                                type: 'line',
                                yAxisIndex: 1,
                                data: this.analysisData.energyConsumption.map(item => item.perKm)
                            }
                        ]
                    });

                    // 初始化高峰时段客流图表
                    const peakHourChart = echarts.init(document.getElementById('peak-hour-chart'));
                    peakHourChart.setOption({
                        title: {
                            text: '高峰时段客流分析',
                            left: 'center'
                        },
                        tooltip: {
                            trigger: 'axis'
                        },
                        grid: {
                            left: '3%',
                            right: '4%',
                            bottom: '3%',
                            containLabel: true
                        },
                        xAxis: {
                            type: 'category',
                            data: this.analysisData.peakHourData.map(item => item.hour)
                        },
                        yAxis: {
                            type: 'value',
                            name: '人次',
                            axisLabel: {
                                formatter: value => {
                                    if (value >= 1000000) {
                                        return (value / 1000000).toFixed(1) + 'M';
                                    } else if (value >= 1000) {
                                        return (value / 1000).toFixed(0) + 'K';
                                    }
                                    return value;
                                }
                            }
                        },
                        series: [{
                            data: this.analysisData.peakHourData.map(item => item.passengers),
                            type: 'line',
                            smooth: true,
                            areaStyle: {},
                            itemStyle: {
                                color: '#67C23A'
                            }
                        }]
                    });

                    // 监听窗口大小变化，重新调整图表大小
                    window.addEventListener('resize', function() {
                        passengerChart.resize();
                        efficiencyChart.resize();
                        energyChart.resize();
                        peakHourChart.resize();
                    });
                },
                changeTimeRange(range) {
                    this.timeRange = range;
                    this.loading = true;
                    // 模拟数据加载
                    setTimeout(() => {
                        this.loading = false;
                        this.loadAnalysisData();
                    }, 500);
                },
                exportData(type) {
                    // 导出数据
                    console.log('导出数据:', type);
                }
            }
        }
    },
    // 其他路由配置...
];

// 创建路由实例
const router = new VueRouter({
    routes
});

// 创建Vue实例
new Vue({
    el: '#app',
    router,
    data: {
        activeMenu: '/',
        currentPage: '监测总览',
        currentTime: '',
        sidebarCollapsed: false
    },
    created() {
        this.updateTime();
        // 每秒更新一次时间
        setInterval(this.updateTime, 1000);
    },
    watch: {
        '$route'(to) {
            this.activeMenu = to.path;
            switch(to.path) {
                case '/':
                    this.currentPage = '监测总览';
                    break;
                case '/train-status':
                    this.currentPage = '列车运行状态';
                    break;
                case '/train-track':
                    this.currentPage = '列车运行轨迹';
                    break;
                case '/warning':
                    this.currentPage = '故障预警';
                    break;
                case '/dispatch':
                    this.currentPage = '车辆调度管理';
                    break;
                case '/data-analysis':
                    this.currentPage = '运行数据分析';
                    break;
                case '/safety':
                    this.currentPage = '安全评估';
                    break;
                case '/statistics':
                    this.currentPage = '运营数据统计';
                    break;
                case '/settings':
                    this.currentPage = '系统设置';
                    break;
            }
        }
    },
    methods: {
        updateTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            this.currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        toggleSidebar() {
            this.sidebarCollapsed = !this.sidebarCollapsed;
            // 切换侧边栏宽度
            const aside = document.querySelector('.el-aside');
            if (aside) {
                aside.style.width = this.sidebarCollapsed ? '64px' : '200px';
            }
        }
    }
}); 