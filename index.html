<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>轨道交通运输列车运行监测系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.10/lib/theme-chalk/index.css">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="https://at.alicdn.com/t/font_2612700_ezxo9l2p9vs.css">
</head>
<body>
    <div id="app">
        <el-container>
            <!-- 侧边栏导航 -->
            <el-aside width="200px">
                <div class="logo">
                    <!-- 移除可能导致404的图片 -->
                    <!-- <img src="images/logo.png" alt="系统logo"> -->
                    <h2>列车运行监测系统</h2>
                </div>
                <el-menu
                    :default-active="activeMenu"
                    class="el-menu-vertical"
                    background-color="#001529"
                    text-color="#fff"
                    active-text-color="#409EFF"
                    router>
                    <el-menu-item index="/">
                        <i class="el-icon-monitor"></i>
                        <span>监测总览</span>
                    </el-menu-item>
                    <el-submenu index="1">
                        <template slot="title">
                            <i class="el-icon-location"></i>
                            <span>列车运行监测</span>
                        </template>
                        <el-menu-item index="/train-status">运行状态</el-menu-item>
                        <el-menu-item index="/train-track">运行轨迹</el-menu-item>
                    </el-submenu>
                    <el-menu-item index="/map" @click="openMap">
                        <i class="el-icon-map-location"></i>
                        <span>列车运行地图</span>
                    </el-menu-item>
                    <el-menu-item index="/warning">
                        <i class="el-icon-warning"></i>
                        <span>故障预警</span>
                    </el-menu-item>
                    <el-menu-item index="/dispatch">
                        <i class="el-icon-truck"></i>
                        <span>车辆调度管理</span>
                    </el-menu-item>
                    <el-menu-item index="/data-analysis">
                        <i class="el-icon-data-analysis"></i>
                        <span>运行数据分析</span>
                    </el-menu-item>
                    <el-menu-item index="/safety">
                        <i class="el-icon-lock"></i>
                        <span>安全评估</span>
                    </el-menu-item>
                    <el-menu-item index="/statistics">
                        <i class="el-icon-s-data"></i>
                        <span>运营数据统计</span>
                    </el-menu-item>
                    <el-menu-item index="/settings">
                        <i class="el-icon-setting"></i>
                        <span>系统设置</span>
                    </el-menu-item>
                </el-menu>
            </el-aside>
            
            <!-- 主要内容区 -->
            <el-container>
                <!-- 头部 -->
                <el-header>
                    <div class="header-left">
                        <el-button type="text" @click="toggleSidebar">
                            <i class="el-icon-s-fold"></i>
                        </el-button>
                        <el-breadcrumb separator="/">
                            <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
                            <el-breadcrumb-item>{{ currentPage }}</el-breadcrumb-item>
                        </el-breadcrumb>
                    </div>
                    <div class="header-right">
                        <span class="time">{{ currentTime }}</span>
                        <el-dropdown trigger="click">
                            <span class="user-info">
                                管理员 <i class="el-icon-arrow-down"></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item>个人信息</el-dropdown-item>
                                <el-dropdown-item>修改密码</el-dropdown-item>
                                <el-dropdown-item divided>退出登录</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                </el-header>
                
                <!-- 内容区 -->
                <el-main>
                    <router-view></router-view>
                </el-main>
            </el-container>
        </el-container>
    </div>

    <!-- 页面模板 -->
    <!-- 监测总览页面模板 -->
    <script type="text/x-template" id="dashboard-template">
        <div class="dashboard-container">
            <el-row :gutter="20">
                <el-col :span="6" v-for="(item, index) in [
                    {title: '列车总数', value: statistics.totalTrains, icon: 'el-icon-truck', color: '#409EFF'},
                    {title: '运行中列车', value: statistics.runningTrains, icon: 'el-icon-video-play', color: '#67C23A'},
                    {title: '故障预警', value: statistics.warningCount, icon: 'el-icon-warning', color: '#E6A23C'},
                    {title: '安全评分', value: statistics.safetyScore, icon: 'el-icon-s-claim', color: '#F56C6C'}
                ]" :key="index">
                    <div class="stat-card">
                        <div class="stat-icon" :style="{backgroundColor: item.color + '20', color: item.color}">
                            <i :class="item.icon"></i>
                        </div>
                        <div class="stat-info">
                            <div class="value">{{item.value}}</div>
                            <div class="title">{{item.title}}</div>
                        </div>
                    </div>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="16">
                    <div class="data-card">
                        <div class="card-header">
                            <div class="card-title">列车运行状态</div>
                            <el-button type="text" icon="el-icon-refresh">刷新</el-button>
                        </div>
                        <div id="train-status-chart" class="chart-container"></div>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="data-card">
                        <div class="card-header">
                            <div class="card-title">最近故障预警</div>
                            <el-button type="text" @click="$router.push('/warning')">查看全部</el-button>
                        </div>
                        <el-table :data="recentWarnings" style="width: 100%" size="small" v-loading="loading">
                            <el-table-column prop="trainId" label="列车号" width="80"></el-table-column>
                            <el-table-column prop="type" label="故障类型"></el-table-column>
                            <el-table-column prop="level" label="级别" width="60">
                                <template slot-scope="scope">
                                    <span :class="getWarningLevelClass(scope.row.level)">{{scope.row.level}}</span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="status" label="状态" width="80"></el-table-column>
                        </el-table>
                    </div>
                </el-col>
            </el-row>

            <el-row :gutter="20">
                <el-col :span="12">
                    <div class="data-card">
                        <div class="card-header">
                            <div class="card-title">故障类型统计</div>
                        </div>
                        <div id="warning-chart" class="chart-container"></div>
                    </div>
                </el-col>
                <el-col :span="12">
                    <div class="data-card">
                        <div class="card-title">运行效率统计</div>
                        <div id="efficiency-chart" class="chart-container"></div>
                    </div>
                </el-col>
            </el-row>

            <div class="data-card">
                <div class="card-header">
                    <div class="card-title">列车实时状态</div>
                    <div>
                        <el-button type="text" @click="$router.push('/train-status')">查看列表</el-button>
                        <el-button type="text" @click="openMap">查看地图</el-button>
                    </div>
                </div>
                <el-table :data="trainStatusData" style="width: 100%" v-loading="loading">
                    <el-table-column prop="trainId" label="列车号" width="100"></el-table-column>
                    <el-table-column prop="line" label="线路" width="100"></el-table-column>
                    <el-table-column prop="status" label="状态" width="120">
                        <template slot-scope="scope">
                            <span class="status-tag" :class="getStatusClass(scope.row.status)">{{scope.row.status}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="speed" label="速度" width="100"></el-table-column>
                    <el-table-column prop="position" label="当前位置"></el-table-column>
                    <el-table-column prop="nextStation" label="下一站" width="120"></el-table-column>
                    <el-table-column prop="arrivalTime" label="预计到达" width="100"></el-table-column>
                </el-table>
            </div>
        </div>
    </script>

    <!-- 列车运行状态页面模板 -->
    <script type="text/x-template" id="train-status-template">
        <div class="train-status-container">
            <!-- 状态概览卡片 -->
            <el-row :gutter="20" style="margin-bottom: 20px;">
                <el-col :span="6" v-for="(stat, index) in statusStats" :key="index">
                    <div class="status-stat-card" :class="'stat-' + stat.type">
                        <div class="stat-icon">
                            <i :class="stat.icon"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">{{ stat.value }}</div>
                            <div class="stat-label">{{ stat.label }}</div>
                            <div class="stat-change" :class="stat.trend">
                                <i :class="getTrendIcon(stat.trend)"></i>
                                {{ stat.change }}
                            </div>
                        </div>
                    </div>
                </el-col>
            </el-row>

            <!-- 地图和实时监控 -->
            <el-row :gutter="20" style="margin-bottom: 20px;">
                <el-col :span="16">
                    <div class="data-card">
                        <div class="card-header">
                            <div class="card-title">列车运行地图</div>
                            <div>
                                <el-button type="primary" size="small" icon="el-icon-refresh" @click="refreshTrainData">刷新数据</el-button>
                                <el-button type="primary" size="small" @click="openMap">全屏查看</el-button>
                                <el-switch v-model="autoRefresh" active-text="自动刷新" inactive-text="" style="margin-left: 10px;"></el-switch>
                            </div>
                        </div>
                        <div class="train-status-map" v-loading="loading">
                            <div id="train-map" style="height: 400px; width: 100%;"></div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="data-card">
                        <div class="card-header">
                            <div class="card-title">实时监控</div>
                        </div>
                        <div class="realtime-monitor">
                            <div class="monitor-item" v-for="(item, index) in realtimeData" :key="index">
                                <div class="monitor-label">{{ item.label }}</div>
                                <div class="monitor-value" :class="item.status">{{ item.value }}</div>
                                <div class="monitor-trend">
                                    <i :class="getTrendIcon(item.trend)"></i>
                                    {{ item.change }}
                                </div>
                            </div>
                        </div>
                    </div>
                </el-col>
            </el-row>

            <!-- 列车状态列表 -->
            <div class="data-card">
                <div class="card-header">
                    <div class="card-title">列车运行状态</div>
                    <div>
                        <el-input
                            placeholder="搜索列车号或线路"
                            v-model="searchQuery"
                            style="width: 200px; margin-right: 10px;"
                            size="small"
                            prefix-icon="el-icon-search">
                        </el-input>
                        <el-select v-model="filterLine" placeholder="线路" size="small" style="width: 100px; margin-right: 10px;">
                            <el-option label="全部线路" value=""></el-option>
                            <el-option label="1号线" value="1号线"></el-option>
                            <el-option label="2号线" value="2号线"></el-option>
                            <el-option label="4号线" value="4号线"></el-option>
                            <el-option label="5号线" value="5号线"></el-option>
                            <el-option label="6号线" value="6号线"></el-option>
                        </el-select>
                        <el-select v-model="filterStatus" placeholder="状态" size="small" style="width: 120px; margin-right: 10px;">
                            <el-option label="全部状态" value=""></el-option>
                            <el-option label="正常运行" value="正常运行"></el-option>
                            <el-option label="减速运行" value="减速运行"></el-option>
                            <el-option label="临时停车" value="临时停车"></el-option>
                        </el-select>
                        <el-button type="success" size="small" icon="el-icon-download" @click="exportTrainData">导出数据</el-button>
                        <el-dropdown @command="handleBatchCommand" style="margin-left: 10px;">
                            <el-button type="warning" size="small">
                                批量操作<i class="el-icon-arrow-down el-icon--right"></i>
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="emergency_stop">紧急停车</el-dropdown-item>
                                <el-dropdown-item command="speed_limit">限速运行</el-dropdown-item>
                                <el-dropdown-item command="normal_operation">恢复正常</el-dropdown-item>
                                <el-dropdown-item command="return_depot">回库</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                </div>
                <el-table :data="filteredTrains" style="width: 100%" v-loading="loading" @row-click="selectTrain">
                    <el-table-column type="selection" width="55"></el-table-column>
                    <el-table-column prop="trainId" label="列车号" width="100" sortable></el-table-column>
                    <el-table-column prop="line" label="线路" width="100" sortable></el-table-column>
                    <el-table-column prop="status" label="状态" width="120" sortable>
                        <template slot-scope="scope">
                            <el-tag :type="getStatusType(scope.row.status)" size="small">{{scope.row.status}}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="speed" label="速度" width="100" sortable>
                        <template slot-scope="scope">
                            <span :style="{color: getSpeedColor(scope.row.speed)}">{{ scope.row.speed }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="position" label="当前位置" min-width="150"></el-table-column>
                    <el-table-column prop="nextStation" label="下一站" width="120"></el-table-column>
                    <el-table-column prop="arrivalTime" label="预计到达" width="100"></el-table-column>
                    <el-table-column prop="temperature" label="温度" width="80">
                        <template slot-scope="scope">
                            <span :style="{color: getTemperatureColor(scope.row.temperature)}">{{ scope.row.temperature }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="humidity" label="湿度" width="80"></el-table-column>
                    <el-table-column prop="passengers" label="乘客情况" width="100">
                        <template slot-scope="scope">
                            <el-tag :type="getPassengerType(scope.row.passengers)" size="small">{{ scope.row.passengers }}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="lastUpdate" label="更新时间" width="150"></el-table-column>
                    <el-table-column label="操作" width="180" fixed="right">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="viewTrainDetail(scope.row)">详情</el-button>
                            <el-button type="text" size="small" @click="viewTrainTrack(scope.row)">轨迹</el-button>
                            <el-button type="text" size="small" @click="controlTrain(scope.row)">控制</el-button>
                            <el-button type="text" size="small" @click="viewTrainHistory(scope.row)">历史</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="trainList.length"
                    style="margin-top: 20px; text-align: right;">
                </el-pagination>
            </div>

            <!-- 列车详情对话框 -->
            <el-dialog title="列车详细信息" :visible.sync="detailDialogVisible" width="80%" top="5vh">
                <div v-if="selectedTrain">
                    <el-tabs v-model="detailActiveTab">
                        <el-tab-pane label="基本信息" name="basic">
                            <el-descriptions :column="3" border>
                                <el-descriptions-item label="列车号">{{ selectedTrain.trainId }}</el-descriptions-item>
                                <el-descriptions-item label="线路">{{ selectedTrain.line }}</el-descriptions-item>
                                <el-descriptions-item label="运行状态">
                                    <el-tag :type="getStatusType(selectedTrain.status)">{{ selectedTrain.status }}</el-tag>
                                </el-descriptions-item>
                                <el-descriptions-item label="当前速度">{{ selectedTrain.speed }}</el-descriptions-item>
                                <el-descriptions-item label="最大速度">{{ selectedTrain.maxSpeed || '80km/h' }}</el-descriptions-item>
                                <el-descriptions-item label="当前位置">{{ selectedTrain.position }}</el-descriptions-item>
                                <el-descriptions-item label="下一站">{{ selectedTrain.nextStation }}</el-descriptions-item>
                                <el-descriptions-item label="预计到达">{{ selectedTrain.arrivalTime }}</el-descriptions-item>
                                <el-descriptions-item label="车厢数量">{{ selectedTrain.carriageCount || '6节' }}</el-descriptions-item>
                                <el-descriptions-item label="载客量">{{ selectedTrain.capacity || '1200人' }}</el-descriptions-item>
                                <el-descriptions-item label="当前载客">{{ selectedTrain.currentPassengers || '680人' }}</el-descriptions-item>
                                <el-descriptions-item label="载客率">{{ selectedTrain.loadRate || '56.7%' }}</el-descriptions-item>
                            </el-descriptions>
                        </el-tab-pane>
                        <el-tab-pane label="设备状态" name="equipment">
                            <el-table :data="selectedTrain.equipmentStatus" style="width: 100%">
                                <el-table-column prop="system" label="系统名称" width="150"></el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="scope.row.status === '正常' ? 'success' : scope.row.status === '警告' ? 'warning' : 'danger'">
                                            {{ scope.row.status }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="value" label="当前值" width="120"></el-table-column>
                                <el-table-column prop="normal" label="正常范围" width="120"></el-table-column>
                                <el-table-column prop="lastCheck" label="最后检查" width="150"></el-table-column>
                                <el-table-column prop="description" label="说明" min-width="200"></el-table-column>
                            </el-table>
                        </el-tab-pane>
                        <el-tab-pane label="性能指标" name="performance">
                            <div style="margin-bottom: 15px;">
                                <el-button-group>
                                    <el-button size="small" @click="refreshPerformanceData">刷新数据</el-button>
                                    <el-button size="small" @click="exportPerformanceData">导出图表</el-button>
                                    <el-button size="small" @click="generateTrainReport(selectedTrain)">生成报告</el-button>
                                </el-button-group>
                            </div>
                            <div id="train-performance-chart" style="height: 400px; width: 100%;"></div>
                            <div style="margin-top: 15px;">
                                <el-alert
                                    v-if="selectedTrain && selectedTrain.recommendations"
                                    title="运维建议"
                                    type="info"
                                    :closable="false">
                                    <ul style="margin: 0; padding-left: 20px;">
                                        <li v-for="(rec, index) in generateRecommendations(selectedTrain)" :key="index">{{ rec }}</li>
                                    </ul>
                                </el-alert>
                            </div>
                        </el-tab-pane>
                        <el-tab-pane label="维护记录" name="maintenance">
                            <el-table :data="selectedTrain.maintenanceHistory" style="width: 100%">
                                <el-table-column prop="date" label="维护日期" width="120"></el-table-column>
                                <el-table-column prop="type" label="维护类型" width="120"></el-table-column>
                                <el-table-column prop="items" label="维护项目" min-width="200"></el-table-column>
                                <el-table-column prop="technician" label="技术员" width="100"></el-table-column>
                                <el-table-column prop="duration" label="耗时" width="80"></el-table-column>
                                <el-table-column prop="cost" label="费用" width="100"></el-table-column>
                                <el-table-column prop="result" label="结果" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="scope.row.result === '合格' ? 'success' : 'warning'">{{ scope.row.result }}</el-tag>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-tab-pane>
                    </el-tabs>
                </div>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                    <el-button type="primary" @click="exportTrainDetail">导出详情</el-button>
                </div>
            </el-dialog>
        </div>
    </script>

    <!-- 列车运行轨迹页面模板 -->
    <script type="text/x-template" id="train-track-template">
        <div class="train-track-container">
            <!-- 轨迹查询控制面板 -->
            <div class="data-card">
                <div class="card-header">
                    <div class="card-title">轨迹查询</div>
                    <div>
                        <el-button type="success" size="small" icon="el-icon-download" @click="exportTrackData">导出轨迹</el-button>
                        <el-button type="primary" size="small" icon="el-icon-data-analysis" @click="generateTrackReport">分析报告</el-button>
                        <el-button type="warning" size="small" icon="el-icon-refresh" @click="clearTrack">清空轨迹</el-button>
                    </div>
                </div>
                <div class="track-controls">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="选择列车">
                                <el-select v-model="selectedTrain" placeholder="选择列车" size="small" style="width: 100%;" @change="onTrainChange">
                                    <el-option
                                        v-for="item in trainOptions"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="查询日期">
                                <el-date-picker
                                    v-model="dateValue"
                                    type="date"
                                    placeholder="选择日期"
                                    size="small"
                                    style="width: 100%;"
                                    @change="onDateChange">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="时间范围">
                                <el-time-picker
                                    v-model="timeRange"
                                    is-range
                                    range-separator="至"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    placeholder="选择时间范围"
                                    size="small"
                                    style="width: 100%;">
                                </el-time-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="操作">
                                <el-button-group style="width: 100%;">
                                    <el-button type="primary" size="small" icon="el-icon-search" @click="queryTrack">查询轨迹</el-button>
                                    <el-button type="success" size="small" icon="el-icon-video-play" @click="playTrack" :disabled="!trackData.length">播放</el-button>
                                </el-button-group>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <!-- 轨迹地图和播放控制 -->
            <el-row :gutter="20" style="margin-bottom: 20px;">
                <el-col :span="16">
                    <div class="data-card">
                        <div class="card-header">
                            <div class="card-title">列车运行轨迹地图</div>
                            <div>
                                <el-switch v-model="showRealTime" active-text="实时轨迹" inactive-text="" style="margin-right: 10px;"></el-switch>
                                <el-button type="primary" size="small" @click="fullscreenMap">全屏查看</el-button>
                            </div>
                        </div>
                        <div class="train-track-map" v-loading="loading">
                            <div id="track-map" style="height: 500px; width: 100%;"></div>
                        </div>

                        <!-- 轨迹播放控制器 -->
                        <div class="track-player" v-if="trackData.length">
                            <div class="player-controls">
                                <el-button-group>
                                    <el-button size="small" icon="el-icon-d-arrow-left" @click="playbackControl('start')">开始</el-button>
                                    <el-button size="small" :icon="isPlaying ? 'el-icon-video-pause' : 'el-icon-video-play'" @click="playbackControl('toggle')">
                                        {{ isPlaying ? '暂停' : '播放' }}
                                    </el-button>
                                    <el-button size="small" icon="el-icon-d-arrow-right" @click="playbackControl('end')">结束</el-button>
                                </el-button-group>
                                <div class="player-progress">
                                    <el-slider
                                        v-model="playbackProgress"
                                        :max="trackData.length - 1"
                                        :step="1"
                                        :show-tooltip="false"
                                        @change="onProgressChange"
                                        style="margin: 0 20px;">
                                    </el-slider>
                                </div>
                                <div class="player-speed">
                                    <span>播放速度:</span>
                                    <el-select v-model="playbackSpeed" size="small" style="width: 80px; margin-left: 5px;">
                                        <el-option label="0.5x" :value="0.5"></el-option>
                                        <el-option label="1x" :value="1"></el-option>
                                        <el-option label="2x" :value="2"></el-option>
                                        <el-option label="4x" :value="4"></el-option>
                                    </el-select>
                                </div>
                                <div class="player-info">
                                    <span v-if="currentTrackPoint">{{ currentTrackPoint.time }} - {{ currentTrackPoint.station }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="8">
                    <div class="data-card">
                        <div class="card-header">
                            <div class="card-title">轨迹统计</div>
                        </div>
                        <div class="track-statistics">
                            <div class="stat-item" v-for="(stat, index) in trackStatistics" :key="index">
                                <div class="stat-label">{{ stat.label }}</div>
                                <div class="stat-value" :style="{color: stat.color}">{{ stat.value }}</div>
                            </div>
                        </div>

                        <!-- 实时分析指标 -->
                        <div class="track-analysis" v-if="trackData.length">
                            <div class="analysis-title">实时分析</div>
                            <div class="analysis-metrics">
                                <div class="metric-item">
                                    <span class="metric-label">安全评分</span>
                                    <span class="metric-value" :class="getSafetyScoreClass(calculateSafetyScore())">
                                        {{ calculateSafetyScore() }}分
                                    </span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">准点率</span>
                                    <span class="metric-value">{{ calculateOnTimePerformance() }}%</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-label">异常事件</span>
                                    <span class="metric-value">{{ countAbnormalEvents() }}次</span>
                                </div>
                            </div>
                        </div>

                        <!-- 轨迹分析图表 -->
                        <div class="track-analysis">
                            <div class="analysis-title">速度分析</div>
                            <div id="speed-analysis-chart" style="height: 200px; width: 100%;"></div>
                        </div>
                    </div>
                </el-col>
            </el-row>

            <!-- 轨迹详情表格 -->
            <div class="data-card">
                <div class="card-header">
                    <div class="card-title">轨迹详情</div>
                    <div>
                        <el-input
                            placeholder="搜索站点或状态"
                            v-model="trackSearchQuery"
                            style="width: 200px; margin-right: 10px;"
                            size="small"
                            prefix-icon="el-icon-search">
                        </el-input>
                        <el-select v-model="trackStatusFilter" placeholder="状态筛选" size="small" style="width: 120px;">
                            <el-option label="全部状态" value=""></el-option>
                            <el-option label="正常运行" value="正常运行"></el-option>
                            <el-option label="进站" value="进站"></el-option>
                            <el-option label="出站" value="出站"></el-option>
                            <el-option label="停车" value="停车"></el-option>
                        </el-select>
                    </div>
                </div>
                <el-table :data="filteredTrackData" style="width: 100%" v-loading="loading" @row-click="locateOnMap">
                    <el-table-column type="index" label="序号" width="60"></el-table-column>
                    <el-table-column prop="time" label="时间" width="150" sortable></el-table-column>
                    <el-table-column prop="station" label="站点/位置" width="150"></el-table-column>
                    <el-table-column prop="speed" label="速度" width="100" sortable>
                        <template slot-scope="scope">
                            <span :style="{color: getSpeedColor(scope.row.speed)}">{{ scope.row.speed }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" width="120" sortable>
                        <template slot-scope="scope">
                            <el-tag :type="getStatusType(scope.row.status)" size="small">{{scope.row.status}}</el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="passengers" label="乘客数量" width="100" sortable></el-table-column>
                    <el-table-column prop="temperature" label="温度" width="80">
                        <template slot-scope="scope">
                            <span :style="{color: getTemperatureColor(scope.row.temperature)}">{{ scope.row.temperature }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="distance" label="累计距离" width="100"></el-table-column>
                    <el-table-column prop="duration" label="运行时长" width="100"></el-table-column>
                    <el-table-column prop="remarks" label="备注" min-width="150"></el-table-column>
                    <el-table-column label="操作" width="120" fixed="right">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="viewTrackDetail(scope.row)">详情</el-button>
                            <el-button type="text" size="small" @click="locateOnMap(scope.row)">定位</el-button>
                        </template>
                    </el-table-column>
                </el-table>

                <!-- 分页 -->
                <el-pagination
                    @size-change="handleTrackSizeChange"
                    @current-change="handleTrackCurrentChange"
                    :current-page="trackCurrentPage"
                    :page-sizes="[20, 50, 100, 200]"
                    :page-size="trackPageSize"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="trackData.length"
                    style="margin-top: 20px; text-align: right;">
                </el-pagination>
            </div>
        </div>
    </script>

    <!-- 故障预警页面模板 -->
    <script type="text/x-template" id="warning-template">
        <div class="warning-container">
            <div class="data-card">
                <div class="card-header">
                    <div class="card-title">故障预警列表</div>
                    <div>
                        <el-input
                            placeholder="搜索故障ID或列车号"
                            v-model="searchQuery"
                            style="width: 200px; margin-right: 10px;"
                            size="small"
                            prefix-icon="el-icon-search">
                        </el-input>
                        <el-select v-model="filterLevel" placeholder="级别" size="small" style="width: 100px; margin-right: 10px;">
                            <el-option label="全部级别" value=""></el-option>
                            <el-option label="高" value="高"></el-option>
                            <el-option label="中" value="中"></el-option>
                            <el-option label="低" value="低"></el-option>
                        </el-select>
                        <el-select v-model="filterStatus" placeholder="状态" size="small" style="width: 120px; margin-right: 10px;">
                            <el-option label="全部状态" value=""></el-option>
                            <el-option label="未处理" value="未处理"></el-option>
                            <el-option label="处理中" value="处理中"></el-option>
                            <el-option label="已处理" value="已处理"></el-option>
                        </el-select>
                        <el-date-picker
                            v-model="dateRange"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            size="small"
                            style="width: 240px;">
                        </el-date-picker>
                    </div>
                </div>
                <el-table :data="filteredWarnings" style="width: 100%" v-loading="loading">
                    <el-table-column prop="id" label="故障ID" width="120"></el-table-column>
                    <el-table-column prop="trainId" label="列车号" width="100"></el-table-column>
                    <el-table-column prop="type" label="故障类型" width="120"></el-table-column>
                    <el-table-column prop="level" label="级别" width="80">
                        <template slot-scope="scope">
                            <span :class="getWarningLevelClass(scope.row.level)">{{scope.row.level}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="time" label="发生时间" width="150"></el-table-column>
                    <el-table-column prop="location" label="位置"></el-table-column>
                    <el-table-column prop="status" label="状态" width="100"></el-table-column>
                    <el-table-column label="操作" width="150">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="viewWarningDetail(scope.row)">详情</el-button>
                            <el-button type="text" size="small" v-if="scope.row.status !== '已处理'" @click="handleWarning(scope.row)">处理</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div style="margin-top: 20px; text-align: right;">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="1"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="warningList.length">
                    </el-pagination>
                </div>
            </div>
        </div>
    </script>

    <!-- 车辆调度管理页面模板 -->
    <script type="text/x-template" id="dispatch-template">
        <div class="dispatch-container">
            <div class="data-card">
                <div class="card-header">
                    <div class="card-title">车辆调度管理</div>
                    <div>
                        <el-button type="primary" size="small" icon="el-icon-plus" @click="addNewDispatch">添加调度</el-button>
                        <el-button type="primary" size="small" icon="el-icon-refresh">刷新数据</el-button>
                    </div>
                </div>
                <div class="filter-section" style="margin-bottom: 15px;">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-input
                                placeholder="搜索调度ID或列车号"
                                v-model="searchQuery"
                                size="small"
                                prefix-icon="el-icon-search">
                            </el-input>
                        </el-col>
                        <el-col :span="4">
                            <el-select v-model="filterLine" placeholder="线路" size="small" style="width: 100%;">
                                <el-option label="全部线路" value=""></el-option>
                                <el-option label="1号线" value="1号线"></el-option>
                                <el-option label="2号线" value="2号线"></el-option>
                                <el-option label="4号线" value="4号线"></el-option>
                                <el-option label="5号线" value="5号线"></el-option>
                                <el-option label="6号线" value="6号线"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="4">
                            <el-select v-model="filterStatus" placeholder="状态" size="small" style="width: 100%;">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="待发车" value="待发车"></el-option>
                                <el-option label="已发车" value="已发车"></el-option>
                                <el-option label="运行中" value="运行中"></el-option>
                                <el-option label="已完成" value="已完成"></el-option>
                                <el-option label="已取消" value="已取消"></el-option>
                                <el-option label="延误" value="延误"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="6">
                            <el-date-picker
                                v-model="dateValue"
                                type="date"
                                placeholder="选择日期"
                                size="small"
                                style="width: 100%;">
                            </el-date-picker>
                        </el-col>
                    </el-row>
                </div>
                <el-table :data="filteredDispatch" style="width: 100%" v-loading="loading">
                    <el-table-column prop="id" label="调度ID" width="120"></el-table-column>
                    <el-table-column prop="trainId" label="列车号" width="100"></el-table-column>
                    <el-table-column prop="line" label="线路" width="80"></el-table-column>
                    <el-table-column prop="driver" label="司机" width="80"></el-table-column>
                    <el-table-column prop="status" label="状态" width="100">
                        <template slot-scope="scope">
                            <span class="status-tag" :class="getStatusClass(scope.row.status)">{{scope.row.status}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column prop="departureTime" label="发车时间" width="150"></el-table-column>
                    <el-table-column prop="arrivalTime" label="预计到达" width="150"></el-table-column>
                    <el-table-column prop="startStation" label="起始站"></el-table-column>
                    <el-table-column prop="endStation" label="终点站"></el-table-column>
                    <el-table-column label="操作" width="150">
                        <template slot-scope="scope">
                            <el-button type="text" size="small" @click="viewDispatchDetail(scope.row)">详情</el-button>
                            <el-button type="text" size="small" @click="editDispatch(scope.row)">编辑</el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <div style="margin-top: 20px; text-align: right;">
                    <el-pagination
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="1"
                        :page-sizes="[10, 20, 50, 100]"
                        :page-size="10"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="dispatchList.length">
                    </el-pagination>
                </div>
            </div>
            
            <!-- 调度详情对话框 -->
            <el-dialog title="调度详情" :visible.sync="detailDialogVisible" width="700px">
                <div v-if="currentDispatch">
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="调度ID">{{ currentDispatch.id }}</el-descriptions-item>
                        <el-descriptions-item label="列车号">{{ currentDispatch.trainId }}</el-descriptions-item>
                        <el-descriptions-item label="线路">{{ currentDispatch.line }}</el-descriptions-item>
                        <el-descriptions-item label="司机">{{ currentDispatch.driver }}</el-descriptions-item>
                        <el-descriptions-item label="状态">
                            <span class="status-tag" :class="getStatusClass(currentDispatch.status)">{{ currentDispatch.status }}</span>
                        </el-descriptions-item>
                        <el-descriptions-item label="创建时间">{{ currentDispatch.createTime || '未知' }}</el-descriptions-item>
                        <el-descriptions-item label="发车时间">{{ currentDispatch.departureTime }}</el-descriptions-item>
                        <el-descriptions-item label="预计到达">{{ currentDispatch.arrivalTime }}</el-descriptions-item>
                        <el-descriptions-item label="起始站">{{ currentDispatch.startStation }}</el-descriptions-item>
                        <el-descriptions-item label="终点站">{{ currentDispatch.endStation }}</el-descriptions-item>
                        <el-descriptions-item label="途经站点" :span="2">{{ currentDispatch.viaStations || '无数据' }}</el-descriptions-item>
                        <el-descriptions-item label="备注" :span="2">{{ currentDispatch.notes || '无' }}</el-descriptions-item>
                    </el-descriptions>
                    
                    <div style="margin-top: 20px;">
                        <el-steps :active="getStepActive()" finish-status="success">
                            <el-step title="计划调度" :description="currentDispatch.createTime"></el-step>
                            <el-step title="待发车" :description="currentDispatch.waitingTime"></el-step>
                            <el-step title="已发车" :description="currentDispatch.departureTime"></el-step>
                            <el-step title="运行中" description=""></el-step>
                            <el-step title="已到达" :description="currentDispatch.actualArrivalTime"></el-step>
                        </el-steps>
                    </div>
                    
                    <div style="margin-top: 20px;" v-if="currentDispatch.status === '运行中'">
                        <div class="card-title" style="margin-bottom: 10px;">实时位置</div>
                        <div style="height: 200px; background-color: #f5f7fa; display: flex; justify-content: center; align-items: center; color: #909399;">
                            <span><i class="el-icon-map-location" style="margin-right: 5px;"></i>地图加载中...</span>
                        </div>
                    </div>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                    <el-button type="primary" @click="editDispatch(currentDispatch)">编辑</el-button>
                </span>
            </el-dialog>
            
            <!-- 添加/编辑调度对话框 -->
            <el-dialog :title="dialogTitle" :visible.sync="formDialogVisible" width="650px">
                <el-form :model="dispatchForm" :rules="dispatchRules" ref="dispatchForm" label-width="100px">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="列车号" prop="trainId">
                                <el-select v-model="dispatchForm.trainId" placeholder="请选择列车" style="width: 100%;">
                                    <el-option v-for="train in availableTrains" :key="train.value" :label="train.label" :value="train.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="线路" prop="line">
                                <el-select v-model="dispatchForm.line" placeholder="请选择线路" style="width: 100%;">
                                    <el-option label="1号线" value="1号线"></el-option>
                                    <el-option label="2号线" value="2号线"></el-option>
                                    <el-option label="4号线" value="4号线"></el-option>
                                    <el-option label="5号线" value="5号线"></el-option>
                                    <el-option label="6号线" value="6号线"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="司机" prop="driver">
                                <el-select v-model="dispatchForm.driver" placeholder="请选择司机" style="width: 100%;">
                                    <el-option v-for="driver in availableDrivers" :key="driver.value" :label="driver.label" :value="driver.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="状态" prop="status">
                                <el-select v-model="dispatchForm.status" placeholder="请选择状态" style="width: 100%;">
                                    <el-option label="待发车" value="待发车"></el-option>
                                    <el-option label="已发车" value="已发车"></el-option>
                                    <el-option label="运行中" value="运行中"></el-option>
                                    <el-option label="已完成" value="已完成"></el-option>
                                    <el-option label="已取消" value="已取消"></el-option>
                                    <el-option label="延误" value="延误"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="发车时间" prop="departureTime">
                                <el-date-picker
                                    v-model="dispatchForm.departureTime"
                                    type="datetime"
                                    placeholder="选择发车时间"
                                    style="width: 100%;">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="预计到达" prop="arrivalTime">
                                <el-date-picker
                                    v-model="dispatchForm.arrivalTime"
                                    type="datetime"
                                    placeholder="选择预计到达时间"
                                    style="width: 100%;">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="起始站" prop="startStation">
                                <el-select v-model="dispatchForm.startStation" placeholder="请选择起始站" style="width: 100%;" @change="updateEndStations">
                                    <el-option v-for="station in stationOptions" :key="station.value" :label="station.label" :value="station.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="终点站" prop="endStation">
                                <el-select v-model="dispatchForm.endStation" placeholder="请选择终点站" style="width: 100%;">
                                    <el-option v-for="station in endStationOptions" :key="station.value" :label="station.label" :value="station.value"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <el-form-item label="途经站点" prop="viaStations">
                        <el-select
                            v-model="dispatchForm.viaStations"
                            multiple
                            placeholder="请选择途经站点"
                            style="width: 100%;">
                            <el-option v-for="station in viaStationOptions" :key="station.value" :label="station.label" :value="station.value"></el-option>
                        </el-select>
                    </el-form-item>
                    
                    <el-form-item label="备注" prop="notes">
                        <el-input type="textarea" v-model="dispatchForm.notes" :rows="3" placeholder="请输入备注信息"></el-input>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="formDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitDispatchForm">确定</el-button>
                </span>
            </el-dialog>
        </div>
    </script>

    <!-- 运行数据分析页面模板 -->
    <script type="text/x-template" id="data-analysis-template">
        <div class="data-analysis-container">
            <div class="data-card">
                <div class="card-header">
                    <div class="card-title">运行数据分析</div>
                    <div>
                        <el-radio-group v-model="timeRange" size="small" @change="changeTimeRange">
                            <el-radio-button label="week">最近7天</el-radio-button>
                            <el-radio-button label="month">最近30天</el-radio-button>
                            <el-radio-button label="quarter">最近90天</el-radio-button>
                        </el-radio-group>
                        <el-dropdown style="margin-left: 10px;">
                            <el-button type="primary" size="small">
                                导出数据<i class="el-icon-arrow-down el-icon--right"></i>
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item @click.native="exportData('excel')">导出Excel</el-dropdown-item>
                                <el-dropdown-item @click.native="exportData('pdf')">导出PDF</el-dropdown-item>
                                <el-dropdown-item @click.native="exportData('image')">导出图表图片</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                </div>
                <div class="filter-section" style="margin-bottom: 15px;">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-select v-model="selectedLine" placeholder="选择线路" size="small" style="width: 100%;">
                                <el-option label="全部线路" value=""></el-option>
                                <el-option label="1号线" value="1号线"></el-option>
                                <el-option label="2号线" value="2号线"></el-option>
                                <el-option label="4号线" value="4号线"></el-option>
                                <el-option label="5号线" value="5号线"></el-option>
                                <el-option label="6号线" value="6号线"></el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="8">
                            <el-date-picker
                                v-model="dateRange"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                size="small"
                                style="width: 100%;">
                            </el-date-picker>
                        </el-col>
                    </el-row>
                </div>

                <!-- 数据概览卡片 -->
                <el-row :gutter="20" style="margin-bottom: 20px;">
                    <el-col :span="6">
                        <div class="data-overview-card">
                            <div class="overview-title">总客流量</div>
                            <div class="overview-value">8,580,000</div>
                            <div class="overview-change" :class="{ 'positive': true }">
                                <i class="el-icon-top"></i> 5.2% 同比上周
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="data-overview-card">
                            <div class="overview-title">平均准点率</div>
                            <div class="overview-value">98.4%</div>
                            <div class="overview-change" :class="{ 'positive': true }">
                                <i class="el-icon-top"></i> 0.3% 同比上周
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="data-overview-card">
                            <div class="overview-title">平均满载率</div>
                            <div class="overview-value">87.9%</div>
                            <div class="overview-change" :class="{ 'positive': false }">
                                <i class="el-icon-bottom"></i> 1.2% 同比上周
                            </div>
                        </div>
                    </el-col>
                    <el-col :span="6">
                        <div class="data-overview-card">
                            <div class="overview-title">总能耗</div>
                            <div class="overview-value">1,796,000 kWh</div>
                            <div class="overview-change" :class="{ 'positive': false }">
                                <i class="el-icon-bottom"></i> 2.5% 同比上周
                            </div>
                        </div>
                    </el-col>
                </el-row>

                <!-- 图表区域 -->
                <el-row :gutter="20">
                    <el-col :span="12">
                        <div class="chart-card">
                            <div class="chart-title">日客流量统计</div>
                            <div id="passenger-chart" class="chart-container" v-loading="loading"></div>
                        </div>
                    </el-col>
                    <el-col :span="12">
                        <div class="chart-card">
                            <div class="chart-title">线路效率对比</div>
                            <div id="efficiency-comparison-chart" class="chart-container" v-loading="loading"></div>
                        </div>
                    </el-col>
                </el-row>

                <el-row :gutter="20" style="margin-top: 20px;">
                    <el-col :span="12">
                        <div class="chart-card">
                            <div class="chart-title">能耗分析</div>
                            <div id="energy-chart" class="chart-container" v-loading="loading"></div>
                        </div>
                    </el-col>
                    <el-col :span="12">
                        <div class="chart-card">
                            <div class="chart-title">高峰时段客流分析</div>
                            <div id="peak-hour-chart" class="chart-container" v-loading="loading"></div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>
    </script>

    <!-- 安全评估页面模板 -->
    <script type="text/x-template" id="safety-template">
        <div class="safety-container">
            <div class="data-card">
                <div class="card-header">
                    <div class="card-title">安全评估总览</div>
                    <div>
                        <el-select v-model="selectedPeriod" placeholder="评估周期" size="small" style="width: 120px; margin-right: 10px;">
                            <el-option label="本日" value="day"></el-option>
                            <el-option label="本周" value="week"></el-option>
                            <el-option label="本月" value="month"></el-option>
                            <el-option label="本季度" value="quarter"></el-option>
                        </el-select>
                        <el-button type="primary" size="small" icon="el-icon-refresh" @click="refreshSafetyData">刷新数据</el-button>
                        <el-button type="success" size="small" icon="el-icon-download" @click="exportSafetyReport">导出报告</el-button>
                    </div>
                </div>
                
                <!-- 安全评分卡片 -->
                <el-row :gutter="20" style="margin-bottom: 20px;">
                    <el-col :span="8">
                        <div class="safety-score-card">
                            <div class="score-title">系统安全评分</div>
                            <div class="score-value">
                                <el-progress type="dashboard" :percentage="safetyScore" :color="getScoreColor"></el-progress>
                                <div class="score-number">{{ safetyScore }}</div>
                            </div>
                            <div class="score-desc">{{ getScoreDesc() }}</div>
                        </div>
                    </el-col>
                    <el-col :span="16">
                        <div class="safety-indicators">
                            <el-row :gutter="20">
                                <el-col :span="8" v-for="(item, index) in safetyIndicators" :key="index">
                                    <div class="indicator-card">
                                        <div class="indicator-title">{{ item.title }}</div>
                                        <div class="indicator-value" :class="getIndicatorClass(item.score)">{{ item.score }}</div>
                                        <el-progress :percentage="item.score" :color="getIndicatorColor(item.score)" :show-text="false"></el-progress>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </el-col>
                </el-row>
                
                <!-- 安全评估详情 -->
                <el-tabs v-model="activeTab">
                    <el-tab-pane label="设备安全" name="equipment">
                        <div class="safety-detail-card">
                            <div class="detail-header">
                                <div class="detail-title">设备安全评估</div>
                                <div class="detail-score" :class="getIndicatorClass(equipmentSafetyScore)">{{ equipmentSafetyScore }}</div>
                            </div>
                            <el-table :data="equipmentSafetyData" style="width: 100%" v-loading="loading">
                                <el-table-column prop="category" label="设备类别" width="150"></el-table-column>
                                <el-table-column prop="checkItems" label="检查项" width="100"></el-table-column>
                                <el-table-column prop="passRate" label="通过率" width="100">
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.passRate }}%</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="score" label="评分" width="100">
                                    <template slot-scope="scope">
                                        <span :class="getIndicatorClass(scope.row.score)">{{ scope.row.score }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="issues" label="问题描述"></el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </div>
                    </el-tab-pane>
                    
                    <el-tab-pane label="运行安全" name="operation">
                        <div class="safety-detail-card">
                            <div class="detail-header">
                                <div class="detail-title">运行安全评估</div>
                                <div class="detail-score" :class="getIndicatorClass(operationSafetyScore)">{{ operationSafetyScore }}</div>
                            </div>
                            <el-table :data="operationSafetyData" style="width: 100%" v-loading="loading">
                                <el-table-column prop="category" label="评估项目" width="150"></el-table-column>
                                <el-table-column prop="standard" label="标准值" width="100"></el-table-column>
                                <el-table-column prop="actual" label="实际值" width="100"></el-table-column>
                                <el-table-column prop="deviation" label="偏差率" width="100">
                                    <template slot-scope="scope">
                                        <span :class="getDeviationClass(scope.row.deviation)">{{ scope.row.deviation }}%</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="score" label="评分" width="100">
                                    <template slot-scope="scope">
                                        <span :class="getIndicatorClass(scope.row.score)">{{ scope.row.score }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="suggestion" label="改进建议"></el-table-column>
                            </el-table>
                        </div>
                    </el-tab-pane>
                    
                    <el-tab-pane label="人员安全" name="personnel">
                        <div class="safety-detail-card">
                            <div class="detail-header">
                                <div class="detail-title">人员安全评估</div>
                                <div class="detail-score" :class="getIndicatorClass(personnelSafetyScore)">{{ personnelSafetyScore }}</div>
                            </div>
                            <el-table :data="personnelSafetyData" style="width: 100%" v-loading="loading">
                                <el-table-column prop="category" label="评估项目" width="150"></el-table-column>
                                <el-table-column prop="standard" label="标准要求" width="180"></el-table-column>
                                <el-table-column prop="completion" label="完成情况" width="100">
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.completion }}%</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="score" label="评分" width="100">
                                    <template slot-scope="scope">
                                        <span :class="getIndicatorClass(scope.row.score)">{{ scope.row.score }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="issues" label="存在问题"></el-table-column>
                                <el-table-column prop="improvement" label="改进措施"></el-table-column>
                            </el-table>
                        </div>
                    </el-tab-pane>
                    
                    <el-tab-pane label="风险预警" name="risks">
                        <div class="safety-detail-card">
                            <div class="detail-header">
                                <div class="detail-title">安全风险预警</div>
                                <el-button type="warning" size="small" icon="el-icon-warning">风险评估</el-button>
                            </div>
                            <el-row :gutter="20" style="margin-bottom: 20px;">
                                <el-col :span="6" v-for="(risk, index) in riskWarnings" :key="index">
                                    <div class="risk-warning-card" :class="'risk-' + risk.level.toLowerCase()">
                                        <div class="risk-header">
                                            <div class="risk-type">{{ risk.type }}</div>
                                            <el-tag :type="getRiskLevelType(risk.level)" size="small">{{ risk.level }}</el-tag>
                                        </div>
                                        <div class="risk-count">{{ risk.count }}个风险点</div>
                                        <div class="risk-trend">
                                            <span>趋势: </span>
                                            <i :class="getRiskTrendIcon(risk.trend)" :style="{color: getRiskTrendColor(risk.trend)}"></i>
                                            <span>{{ getRiskTrendText(risk.trend) }}</span>
                                        </div>
                                        <div class="risk-desc">{{ risk.description }}</div>
                                    </div>
                                </el-col>
                            </el-row>
                            <div class="risk-analysis">
                                <div class="analysis-title">风险分析建议</div>
                                <el-alert
                                    title="高风险提醒"
                                    type="warning"
                                    description="节假日客流高峰期间，建议增加安全巡检频次，确保设备正常运行。"
                                    show-icon
                                    :closable="false">
                                </el-alert>
                                <el-alert
                                    title="设备维护提醒"
                                    type="info"
                                    description="部分设备接近维护周期，建议提前安排维护计划，避免影响正常运营。"
                                    show-icon
                                    :closable="false"
                                    style="margin-top: 10px;">
                                </el-alert>
                            </div>
                        </div>
                    </el-tab-pane>

                    <el-tab-pane label="安全事件" name="incidents">
                        <!-- 事件统计概览 -->
                        <div class="safety-detail-card" style="margin-bottom: 20px;">
                            <div class="detail-header">
                                <div class="detail-title">事件统计概览</div>
                                <el-button type="success" size="small" icon="el-icon-document" @click="generateIncidentReport">生成报告</el-button>
                            </div>
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <div class="incident-stat-card">
                                        <div class="stat-number">{{ incidentStatistics.totalIncidents }}</div>
                                        <div class="stat-label">总事件数</div>
                                    </div>
                                </el-col>
                                <el-col :span="6">
                                    <div class="incident-stat-card">
                                        <div class="stat-number">{{ incidentStatistics.resolvedIncidents }}</div>
                                        <div class="stat-label">已处理</div>
                                    </div>
                                </el-col>
                                <el-col :span="6">
                                    <div class="incident-stat-card">
                                        <div class="stat-number">{{ incidentStatistics.pendingIncidents }}</div>
                                        <div class="stat-label">待处理</div>
                                    </div>
                                </el-col>
                                <el-col :span="6">
                                    <div class="incident-stat-card">
                                        <div class="stat-number">{{ incidentStatistics.avgResolutionTime }}h</div>
                                        <div class="stat-label">平均处理时间</div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>

                        <div class="safety-detail-card">
                            <div class="detail-header">
                                <div class="detail-title">安全事件记录</div>
                                <el-button type="primary" size="small" icon="el-icon-plus" @click="addSafetyIncident">添加事件</el-button>
                            </div>
                            <el-table :data="safetyIncidents" style="width: 100%" v-loading="loading">
                                <el-table-column prop="id" label="事件ID" width="120"></el-table-column>
                                <el-table-column prop="time" label="发生时间" width="150"></el-table-column>
                                <el-table-column prop="type" label="事件类型" width="120"></el-table-column>
                                <el-table-column prop="level" label="级别" width="80">
                                    <template slot-scope="scope">
                                        <el-tag :type="getIncidentLevelType(scope.row.level)">{{ scope.row.level }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="location" label="发生位置" width="150"></el-table-column>
                                <el-table-column prop="description" label="事件描述"></el-table-column>
                                <el-table-column prop="status" label="处理状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getIncidentStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="120">
                                    <template slot-scope="scope">
                                        <el-button type="text" size="small" @click="viewIncidentDetail(scope.row)">详情</el-button>
                                        <el-button type="text" size="small" @click="editIncident(scope.row)">编辑</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <div style="margin-top: 20px; text-align: right;">
                                <el-pagination
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange"
                                    :current-page="1"
                                    :page-sizes="[10, 20, 50, 100]"
                                    :page-size="10"
                                    layout="total, sizes, prev, pager, next, jumper"
                                    :total="safetyIncidents.length">
                                </el-pagination>
                            </div>
                        </div>
                    </el-tab-pane>
                </el-tabs>
                
                <!-- 安全趋势图表 -->
                <el-row :gutter="20" style="margin-top: 20px;">
                    <el-col :span="12">
                        <div class="safety-trend-chart">
                            <div class="chart-title">安全评分趋势分析</div>
                            <div id="safety-trend-chart" class="chart-container" v-loading="loading"></div>
                        </div>
                    </el-col>
                    <el-col :span="12">
                        <div class="safety-trend-chart">
                            <div class="chart-title">事件类型分布</div>
                            <div id="incident-type-chart" class="chart-container" v-loading="loading"></div>
                        </div>
                    </el-col>
                </el-row>

                <!-- 风险分析图表 -->
                <el-row :gutter="20" style="margin-top: 20px;">
                    <el-col :span="12">
                        <div class="safety-trend-chart">
                            <div class="chart-title">风险等级分布</div>
                            <div id="risk-level-chart" class="chart-container" v-loading="loading"></div>
                        </div>
                    </el-col>
                    <el-col :span="12">
                        <div class="safety-trend-chart">
                            <div class="chart-title">月度事件趋势</div>
                            <div id="monthly-incident-chart" class="chart-container" v-loading="loading"></div>
                        </div>
                    </el-col>
                </el-row>
            </div>
            
            <!-- 安全事件详情对话框 -->
            <el-dialog title="安全事件详情" :visible.sync="incidentDialogVisible" width="700px">
                <div v-if="currentIncident">
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="事件ID">{{ currentIncident.id }}</el-descriptions-item>
                        <el-descriptions-item label="事件类型">{{ currentIncident.type }}</el-descriptions-item>
                        <el-descriptions-item label="发生时间">{{ currentIncident.time }}</el-descriptions-item>
                        <el-descriptions-item label="级别">
                            <el-tag :type="getIncidentLevelType(currentIncident.level)">{{ currentIncident.level }}</el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="发生位置">{{ currentIncident.location }}</el-descriptions-item>
                        <el-descriptions-item label="处理状态">
                            <el-tag :type="getIncidentStatusType(currentIncident.status)">{{ currentIncident.status }}</el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="事件描述" :span="2">{{ currentIncident.description }}</el-descriptions-item>
                        <el-descriptions-item label="影响范围" :span="2">{{ currentIncident.impact }}</el-descriptions-item>
                        <el-descriptions-item label="处理措施" :span="2">{{ currentIncident.measures }}</el-descriptions-item>
                        <el-descriptions-item label="责任人">{{ currentIncident.responsible }}</el-descriptions-item>
                        <el-descriptions-item label="记录人">{{ currentIncident.recorder }}</el-descriptions-item>
                    </el-descriptions>
                    
                    <div style="margin-top: 20px;">
                        <div class="detail-title">处理进度</div>
                        <el-steps :active="getIncidentStepActive()" finish-status="success">
                            <el-step title="事件发生" :description="currentIncident.time"></el-step>
                            <el-step title="事件上报" :description="currentIncident.reportTime"></el-step>
                            <el-step title="处理中" :description="currentIncident.processingTime"></el-step>
                            <el-step title="处理完成" :description="currentIncident.resolvedTime"></el-step>
                            <el-step title="复核确认" :description="currentIncident.reviewTime"></el-step>
                        </el-steps>
                    </div>
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="incidentDialogVisible = false">关闭</el-button>
                    <el-button type="primary" @click="editIncident(currentIncident)">编辑</el-button>
                </span>
            </el-dialog>
            
            <!-- 添加/编辑安全事件对话框 -->
            <el-dialog :title="incidentDialogTitle" :visible.sync="incidentFormDialogVisible" width="650px">
                <el-form :model="incidentForm" :rules="incidentRules" ref="incidentForm" label-width="100px">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="事件类型" prop="type">
                                <el-select v-model="incidentForm.type" placeholder="请选择事件类型" style="width: 100%;">
                                    <el-option label="设备故障" value="设备故障"></el-option>
                                    <el-option label="人员操作失误" value="人员操作失误"></el-option>
                                    <el-option label="外部因素" value="外部因素"></el-option>
                                    <el-option label="系统异常" value="系统异常"></el-option>
                                    <el-option label="其他" value="其他"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="级别" prop="level">
                                <el-select v-model="incidentForm.level" placeholder="请选择级别" style="width: 100%;">
                                    <el-option label="轻微" value="轻微"></el-option>
                                    <el-option label="一般" value="一般"></el-option>
                                    <el-option label="严重" value="严重"></el-option>
                                    <el-option label="紧急" value="紧急"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="发生时间" prop="time">
                                <el-date-picker
                                    v-model="incidentForm.time"
                                    type="datetime"
                                    placeholder="选择发生时间"
                                    style="width: 100%;">
                                </el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="发生位置" prop="location">
                                <el-input v-model="incidentForm.location" placeholder="请输入发生位置"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <el-form-item label="事件描述" prop="description">
                        <el-input type="textarea" v-model="incidentForm.description" :rows="3" placeholder="请输入事件描述"></el-input>
                    </el-form-item>
                    
                    <el-form-item label="影响范围" prop="impact">
                        <el-input type="textarea" v-model="incidentForm.impact" :rows="2" placeholder="请输入影响范围"></el-input>
                    </el-form-item>
                    
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="处理状态" prop="status">
                                <el-select v-model="incidentForm.status" placeholder="请选择处理状态" style="width: 100%;">
                                    <el-option label="未处理" value="未处理"></el-option>
                                    <el-option label="处理中" value="处理中"></el-option>
                                    <el-option label="已处理" value="已处理"></el-option>
                                    <el-option label="已关闭" value="已关闭"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="责任人" prop="responsible">
                                <el-input v-model="incidentForm.responsible" placeholder="请输入责任人"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <el-form-item label="处理措施" prop="measures">
                        <el-input type="textarea" v-model="incidentForm.measures" :rows="3" placeholder="请输入处理措施"></el-input>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="incidentFormDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitIncidentForm">确定</el-button>
                </span>
            </el-dialog>
        </div>
    </script>

    <!-- 运营数据统计页面模板 -->
    <script type="text/x-template" id="statistics-template">
        <div class="statistics-container">
            <!-- 数据概览卡片 -->
            <div class="data-card">
                <div class="card-header">
                    <div class="card-title">运营数据概览</div>
                    <div>
                        <el-radio-group v-model="timeRange" size="small" @change="changeTimeRange">
                            <el-radio-button label="today">今日</el-radio-button>
                            <el-radio-button label="week">本周</el-radio-button>
                            <el-radio-button label="month">本月</el-radio-button>
                            <el-radio-button label="year">本年</el-radio-button>
                        </el-radio-group>
                        <el-dropdown style="margin-left: 10px;">
                            <el-button type="primary" size="small">
                                导出数据<i class="el-icon-arrow-down el-icon--right"></i>
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item @click.native="exportData('excel')">导出Excel</el-dropdown-item>
                                <el-dropdown-item @click.native="exportData('pdf')">导出PDF</el-dropdown-item>
                                <el-dropdown-item @click.native="exportData('image')">导出图表</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </div>
                </div>

                <!-- 核心指标卡片 -->
                <el-row :gutter="20" style="margin-bottom: 20px;">
                    <el-col :span="6" v-for="(metric, index) in coreMetrics" :key="index">
                        <div class="metric-card" :class="'metric-' + metric.type">
                            <div class="metric-icon">
                                <i :class="metric.icon"></i>
                            </div>
                            <div class="metric-content">
                                <div class="metric-value">{{ metric.value }}</div>
                                <div class="metric-label">{{ metric.label }}</div>
                                <div class="metric-change" :class="metric.trend">
                                    <i :class="getTrendIcon(metric.trend)"></i>
                                    {{ metric.change }}
                                </div>
                            </div>
                        </div>
                    </el-col>
                </el-row>
            </div>

            <!-- 数据分析标签页 -->
            <el-tabs v-model="activeTab" type="border-card">
                <el-tab-pane label="客流分析" name="passenger">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <div class="chart-card">
                                <div class="chart-title">日客流量趋势</div>
                                <div id="passenger-trend-chart" class="chart-container" v-loading="loading"></div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="chart-card">
                                <div class="chart-title">各线路客流分布</div>
                                <div id="passenger-line-chart" class="chart-container" v-loading="loading"></div>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20" style="margin-top: 20px;">
                        <el-col :span="12">
                            <div class="chart-card">
                                <div class="chart-title">高峰时段客流分析</div>
                                <div id="passenger-peak-chart" class="chart-container" v-loading="loading"></div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="chart-card">
                                <div class="chart-title">客流热力图</div>
                                <div id="passenger-heatmap-chart" class="chart-container" v-loading="loading"></div>
                            </div>
                        </el-col>
                    </el-row>
                </el-tab-pane>

                <el-tab-pane label="运行效率" name="efficiency">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <div class="chart-card">
                                <div class="chart-title">各线路运行效率对比</div>
                                <div id="efficiency-comparison-chart" class="chart-container" v-loading="loading"></div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="chart-card">
                                <div class="chart-title">准点率趋势</div>
                                <div id="punctuality-trend-chart" class="chart-container" v-loading="loading"></div>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20" style="margin-top: 20px;">
                        <el-col :span="24">
                            <div class="chart-card">
                                <div class="chart-title">运行效率详细分析</div>
                                <el-table :data="efficiencyData" style="width: 100%">
                                    <el-table-column prop="line" label="线路" width="100"></el-table-column>
                                    <el-table-column prop="punctuality" label="准点率" width="100">
                                        <template slot-scope="scope">
                                            <span>{{ scope.row.punctuality }}%</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="loadFactor" label="满载率" width="100">
                                        <template slot-scope="scope">
                                            <span>{{ scope.row.loadFactor }}%</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="avgSpeed" label="平均速度" width="120">
                                        <template slot-scope="scope">
                                            <span>{{ scope.row.avgSpeed }} km/h</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="efficiency" label="运行效率" width="100">
                                        <template slot-scope="scope">
                                            <el-progress :percentage="scope.row.efficiency" :color="getEfficiencyColor(scope.row.efficiency)"></el-progress>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="dailyTrips" label="日班次" width="100"></el-table-column>
                                    <el-table-column prop="totalDistance" label="总里程(km)" width="120"></el-table-column>
                                    <el-table-column prop="energyConsumption" label="能耗(kWh)" width="120"></el-table-column>
                                </el-table>
                            </div>
                        </el-col>
                    </el-row>
                </el-tab-pane>

                <el-tab-pane label="收入分析" name="revenue">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <div class="chart-card">
                                <div class="chart-title">月度收入趋势</div>
                                <div id="revenue-trend-chart" class="chart-container" v-loading="loading"></div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="chart-card">
                                <div class="chart-title">各线路收入占比</div>
                                <div id="revenue-line-chart" class="chart-container" v-loading="loading"></div>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20" style="margin-top: 20px;">
                        <el-col :span="8">
                            <div class="revenue-summary-card">
                                <div class="summary-title">总收入</div>
                                <div class="summary-value">¥{{ formatNumber(revenueData.total) }}</div>
                                <div class="summary-change positive">
                                    <i class="el-icon-top"></i> {{ revenueData.totalChange }}%
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="revenue-summary-card">
                                <div class="summary-title">平均票价</div>
                                <div class="summary-value">¥{{ revenueData.avgTicketPrice }}</div>
                                <div class="summary-change negative">
                                    <i class="el-icon-bottom"></i> {{ revenueData.priceChange }}%
                                </div>
                            </div>
                        </el-col>
                        <el-col :span="8">
                            <div class="revenue-summary-card">
                                <div class="summary-title">客单价</div>
                                <div class="summary-value">¥{{ revenueData.avgRevPerPassenger }}</div>
                                <div class="summary-change positive">
                                    <i class="el-icon-top"></i> {{ revenueData.revenuePerPassengerChange }}%
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </el-tab-pane>

                <el-tab-pane label="设备利用率" name="equipment">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <div class="chart-card">
                                <div class="chart-title">设备利用率仪表盘</div>
                                <div id="equipment-gauge-chart" class="chart-container" v-loading="loading"></div>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="chart-card">
                                <div class="chart-title">设备状态分布</div>
                                <div id="equipment-status-chart" class="chart-container" v-loading="loading"></div>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20" style="margin-top: 20px;">
                        <el-col :span="24">
                            <div class="chart-card">
                                <div class="chart-title">设备维护计划</div>
                                <el-table :data="equipmentData" style="width: 100%">
                                    <el-table-column prop="equipment" label="设备名称" width="150"></el-table-column>
                                    <el-table-column prop="type" label="设备类型" width="120"></el-table-column>
                                    <el-table-column prop="utilization" label="利用率" width="100">
                                        <template slot-scope="scope">
                                            <el-progress :percentage="scope.row.utilization" :color="getUtilizationColor(scope.row.utilization)"></el-progress>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="status" label="运行状态" width="100">
                                        <template slot-scope="scope">
                                            <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="lastMaintenance" label="上次维护" width="120"></el-table-column>
                                    <el-table-column prop="nextMaintenance" label="下次维护" width="120"></el-table-column>
                                    <el-table-column prop="maintenanceCost" label="维护成本" width="100"></el-table-column>
                                    <el-table-column label="操作" width="150">
                                        <template slot-scope="scope">
                                            <el-button type="text" size="small" @click="viewEquipmentDetail(scope.row)">详情</el-button>
                                            <el-button type="text" size="small" @click="scheduleMaintenace(scope.row)">安排维护</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </div>
                        </el-col>
                    </el-row>
                </el-tab-pane>
            </el-tabs>
        </div>
    </script>

    <!-- 系统设置页面模板 -->
    <script type="text/x-template" id="settings-template">
        <div class="settings-container">
            <!-- 设置导航标签页 -->
            <el-tabs v-model="activeTab" type="border-card">
                <!-- 用户管理 -->
                <el-tab-pane label="用户管理" name="users">
                    <div class="settings-section">
                        <div class="section-header">
                            <div class="section-title">用户管理</div>
                            <el-button type="primary" size="small" icon="el-icon-plus" @click="addUser">添加用户</el-button>
                        </div>

                        <!-- 用户搜索和筛选 -->
                        <div class="filter-bar">
                            <el-input
                                placeholder="搜索用户名或姓名"
                                v-model="userSearchQuery"
                                style="width: 200px; margin-right: 10px;"
                                size="small"
                                prefix-icon="el-icon-search">
                            </el-input>
                            <el-select v-model="userRoleFilter" placeholder="角色" size="small" style="width: 120px; margin-right: 10px;">
                                <el-option label="全部角色" value=""></el-option>
                                <el-option label="管理员" value="admin"></el-option>
                                <el-option label="操作员" value="operator"></el-option>
                                <el-option label="观察员" value="viewer"></el-option>
                            </el-select>
                            <el-select v-model="userStatusFilter" placeholder="状态" size="small" style="width: 100px;">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="启用" value="active"></el-option>
                                <el-option label="禁用" value="disabled"></el-option>
                            </el-select>
                        </div>

                        <!-- 用户列表 -->
                        <el-table :data="filteredUsers" style="width: 100%">
                            <el-table-column prop="id" label="用户ID" width="80"></el-table-column>
                            <el-table-column prop="username" label="用户名" width="120"></el-table-column>
                            <el-table-column prop="realName" label="真实姓名" width="120"></el-table-column>
                            <el-table-column prop="email" label="邮箱" width="180"></el-table-column>
                            <el-table-column prop="role" label="角色" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="getRoleType(scope.row.role)">{{ getRoleText(scope.row.role) }}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="status" label="状态" width="80">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                                        {{ scope.row.status === 'active' ? '启用' : '禁用' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="lastLogin" label="最后登录" width="150"></el-table-column>
                            <el-table-column prop="createTime" label="创建时间" width="150"></el-table-column>
                            <el-table-column label="操作" width="200">
                                <template slot-scope="scope">
                                    <el-button type="text" size="small" @click="editUser(scope.row)">编辑</el-button>
                                    <el-button type="text" size="small" @click="resetPassword(scope.row)">重置密码</el-button>
                                    <el-button type="text" size="small" @click="toggleUserStatus(scope.row)">
                                        {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                                    </el-button>
                                    <el-button type="text" size="small" style="color: #F56C6C;" @click="deleteUser(scope.row)">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 分页 -->
                        <el-pagination
                            @size-change="handleUserSizeChange"
                            @current-change="handleUserCurrentChange"
                            :current-page="userCurrentPage"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="userPageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="users.length"
                            style="margin-top: 20px; text-align: right;">
                        </el-pagination>
                    </div>
                </el-tab-pane>

                <!-- 角色权限 -->
                <el-tab-pane label="角色权限" name="roles">
                    <div class="settings-section">
                        <div class="section-header">
                            <div class="section-title">角色权限管理</div>
                            <el-button type="primary" size="small" icon="el-icon-plus" @click="addRole">添加角色</el-button>
                        </div>

                        <!-- 角色列表 -->
                        <el-table :data="roles" style="width: 100%">
                            <el-table-column prop="id" label="角色ID" width="80"></el-table-column>
                            <el-table-column prop="name" label="角色名称" width="120"></el-table-column>
                            <el-table-column prop="description" label="角色描述" width="200"></el-table-column>
                            <el-table-column prop="userCount" label="用户数量" width="100"></el-table-column>
                            <el-table-column label="权限" min-width="300">
                                <template slot-scope="scope">
                                    <el-tag v-for="permission in scope.row.permissions" :key="permission" size="small" style="margin-right: 5px;">
                                        {{ getPermissionText(permission) }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="createTime" label="创建时间" width="150"></el-table-column>
                            <el-table-column label="操作" width="150">
                                <template slot-scope="scope">
                                    <el-button type="text" size="small" @click="editRole(scope.row)">编辑</el-button>
                                    <el-button type="text" size="small" @click="editRolePermissions(scope.row)">权限</el-button>
                                    <el-button type="text" size="small" style="color: #F56C6C;" @click="deleteRole(scope.row)" :disabled="scope.row.isSystem">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>

                <!-- 系统配置 -->
                <el-tab-pane label="系统配置" name="system">
                    <div class="settings-section">
                        <div class="section-header">
                            <div class="section-title">系统参数配置</div>
                            <el-button type="success" size="small" icon="el-icon-check" @click="saveSystemConfig">保存配置</el-button>
                        </div>

                        <el-form :model="systemConfig" label-width="150px">
                            <el-card class="config-card">
                                <div slot="header">基础配置</div>
                                <el-form-item label="系统名称">
                                    <el-input v-model="systemConfig.systemName" placeholder="请输入系统名称"></el-input>
                                </el-form-item>
                                <el-form-item label="系统版本">
                                    <el-input v-model="systemConfig.systemVersion" placeholder="请输入系统版本"></el-input>
                                </el-form-item>
                                <el-form-item label="会话超时时间">
                                    <el-input-number v-model="systemConfig.sessionTimeout" :min="5" :max="1440" placeholder="分钟"></el-input-number>
                                    <span style="margin-left: 10px; color: #909399;">分钟</span>
                                </el-form-item>
                                <el-form-item label="密码复杂度">
                                    <el-switch v-model="systemConfig.passwordComplexity" active-text="启用" inactive-text="禁用"></el-switch>
                                </el-form-item>
                            </el-card>

                            <el-card class="config-card">
                                <div slot="header">监控配置</div>
                                <el-form-item label="数据刷新间隔">
                                    <el-input-number v-model="systemConfig.dataRefreshInterval" :min="1" :max="60" placeholder="秒"></el-input-number>
                                    <span style="margin-left: 10px; color: #909399;">秒</span>
                                </el-form-item>
                                <el-form-item label="预警阈值设置">
                                    <el-row :gutter="20">
                                        <el-col :span="8">
                                            <el-form-item label="温度预警(°C)">
                                                <el-input-number v-model="systemConfig.thresholds.temperature" :min="0" :max="100"></el-input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="速度预警(km/h)">
                                                <el-input-number v-model="systemConfig.thresholds.speed" :min="0" :max="200"></el-input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="压力预警(MPa)">
                                                <el-input-number v-model="systemConfig.thresholds.pressure" :min="0" :max="10" :precision="1"></el-input-number>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form-item>
                            </el-card>

                            <el-card class="config-card">
                                <div slot="header">通知配置</div>
                                <el-form-item label="邮件通知">
                                    <el-switch v-model="systemConfig.notifications.email" active-text="启用" inactive-text="禁用"></el-switch>
                                </el-form-item>
                                <el-form-item label="短信通知">
                                    <el-switch v-model="systemConfig.notifications.sms" active-text="启用" inactive-text="禁用"></el-switch>
                                </el-form-item>
                                <el-form-item label="系统通知">
                                    <el-switch v-model="systemConfig.notifications.system" active-text="启用" inactive-text="禁用"></el-switch>
                                </el-form-item>
                                <el-form-item label="SMTP服务器" v-if="systemConfig.notifications.email">
                                    <el-input v-model="systemConfig.emailConfig.smtpServer" placeholder="请输入SMTP服务器地址"></el-input>
                                </el-form-item>
                                <el-form-item label="SMTP端口" v-if="systemConfig.notifications.email">
                                    <el-input-number v-model="systemConfig.emailConfig.smtpPort" :min="1" :max="65535"></el-input-number>
                                </el-form-item>
                            </el-card>
                        </el-form>
                    </div>
                </el-tab-pane>

                <!-- 数据备份 -->
                <el-tab-pane label="数据备份" name="backup">
                    <div class="settings-section">
                        <div class="section-header">
                            <div class="section-title">数据备份管理</div>
                            <div>
                                <el-button type="primary" size="small" icon="el-icon-download" @click="createBackup">立即备份</el-button>
                                <el-button type="success" size="small" icon="el-icon-setting" @click="configAutoBackup">自动备份设置</el-button>
                            </div>
                        </div>

                        <!-- 备份配置 -->
                        <el-card class="config-card">
                            <div slot="header">自动备份配置</div>
                            <el-form :model="backupConfig" label-width="120px">
                                <el-form-item label="自动备份">
                                    <el-switch v-model="backupConfig.autoBackup" active-text="启用" inactive-text="禁用"></el-switch>
                                </el-form-item>
                                <el-form-item label="备份频率" v-if="backupConfig.autoBackup">
                                    <el-select v-model="backupConfig.frequency" placeholder="请选择备份频率">
                                        <el-option label="每日" value="daily"></el-option>
                                        <el-option label="每周" value="weekly"></el-option>
                                        <el-option label="每月" value="monthly"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="备份时间" v-if="backupConfig.autoBackup">
                                    <el-time-picker v-model="backupConfig.backupTime" placeholder="选择备份时间"></el-time-picker>
                                </el-form-item>
                                <el-form-item label="保留天数">
                                    <el-input-number v-model="backupConfig.retentionDays" :min="1" :max="365"></el-input-number>
                                    <span style="margin-left: 10px; color: #909399;">天</span>
                                </el-form-item>
                            </el-form>
                        </el-card>

                        <!-- 备份文件列表 -->
                        <el-card class="config-card">
                            <div slot="header">备份文件列表</div>
                            <el-table :data="backupFiles" style="width: 100%">
                                <el-table-column prop="filename" label="文件名" width="300"></el-table-column>
                                <el-table-column prop="size" label="文件大小" width="120"></el-table-column>
                                <el-table-column prop="type" label="备份类型" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="scope.row.type === 'auto' ? 'success' : 'primary'">
                                            {{ scope.row.type === 'auto' ? '自动' : '手动' }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="createTime" label="创建时间" width="150"></el-table-column>
                                <el-table-column prop="status" label="状态" width="100">
                                    <template slot-scope="scope">
                                        <el-tag :type="getBackupStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="200">
                                    <template slot-scope="scope">
                                        <el-button type="text" size="small" @click="downloadBackup(scope.row)">下载</el-button>
                                        <el-button type="text" size="small" @click="restoreBackup(scope.row)" :disabled="scope.row.status !== '完成'">恢复</el-button>
                                        <el-button type="text" size="small" style="color: #F56C6C;" @click="deleteBackup(scope.row)">删除</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-card>
                    </div>
                </el-tab-pane>

                <!-- 系统监控 -->
                <el-tab-pane label="系统监控" name="monitor">
                    <div class="settings-section">
                        <div class="section-header">
                            <div class="section-title">系统监控</div>
                            <el-button type="primary" size="small" icon="el-icon-refresh" @click="refreshSystemStatus">刷新状态</el-button>
                        </div>

                        <!-- 系统状态概览 -->
                        <el-row :gutter="20" style="margin-bottom: 20px;">
                            <el-col :span="6" v-for="(metric, index) in systemMetrics" :key="index">
                                <div class="system-metric-card">
                                    <div class="metric-icon" :style="{color: metric.color}">
                                        <i :class="metric.icon"></i>
                                    </div>
                                    <div class="metric-content">
                                        <div class="metric-value">{{ metric.value }}</div>
                                        <div class="metric-label">{{ metric.label }}</div>
                                        <div class="metric-status" :class="metric.status">{{ metric.statusText }}</div>
                                    </div>
                                </div>
                            </el-col>
                        </el-row>

                        <!-- 系统日志 -->
                        <el-card class="config-card">
                            <div slot="header">
                                <span>系统日志</span>
                                <el-button style="float: right; padding: 3px 0" type="text" @click="clearLogs">清空日志</el-button>
                            </div>
                            <div class="log-filter">
                                <el-select v-model="logLevelFilter" placeholder="日志级别" size="small" style="width: 120px; margin-right: 10px;">
                                    <el-option label="全部级别" value=""></el-option>
                                    <el-option label="错误" value="error"></el-option>
                                    <el-option label="警告" value="warning"></el-option>
                                    <el-option label="信息" value="info"></el-option>
                                    <el-option label="调试" value="debug"></el-option>
                                </el-select>
                                <el-date-picker
                                    v-model="logDateRange"
                                    type="datetimerange"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    end-placeholder="结束日期"
                                    size="small"
                                    style="margin-right: 10px;">
                                </el-date-picker>
                                <el-button type="primary" size="small" @click="searchLogs">查询</el-button>
                            </div>
                            <el-table :data="filteredLogs" style="width: 100%; margin-top: 15px;" max-height="400">
                                <el-table-column prop="time" label="时间" width="150"></el-table-column>
                                <el-table-column prop="level" label="级别" width="80">
                                    <template slot-scope="scope">
                                        <el-tag :type="getLogLevelType(scope.row.level)" size="small">{{ scope.row.level }}</el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="module" label="模块" width="120"></el-table-column>
                                <el-table-column prop="message" label="消息" min-width="300"></el-table-column>
                                <el-table-column prop="user" label="用户" width="100"></el-table-column>
                            </el-table>
                        </el-card>
                    </div>
                </el-tab-pane>
            </el-tabs>

            <!-- 用户编辑对话框 -->
            <el-dialog :title="userDialogTitle" :visible.sync="userDialogVisible" width="600px">
                <el-form :model="userForm" :rules="userRules" ref="userForm" label-width="100px">
                    <el-form-item label="用户名" prop="username">
                        <el-input v-model="userForm.username" :disabled="userForm.id"></el-input>
                    </el-form-item>
                    <el-form-item label="真实姓名" prop="realName">
                        <el-input v-model="userForm.realName"></el-input>
                    </el-form-item>
                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model="userForm.email"></el-input>
                    </el-form-item>
                    <el-form-item label="手机号" prop="phone">
                        <el-input v-model="userForm.phone"></el-input>
                    </el-form-item>
                    <el-form-item label="角色" prop="role">
                        <el-select v-model="userForm.role" placeholder="请选择角色">
                            <el-option label="管理员" value="admin"></el-option>
                            <el-option label="操作员" value="operator"></el-option>
                            <el-option label="观察员" value="viewer"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-radio-group v-model="userForm.status">
                            <el-radio label="active">启用</el-radio>
                            <el-radio label="disabled">禁用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="密码" prop="password" v-if="!userForm.id">
                        <el-input v-model="userForm.password" type="password" placeholder="请输入密码"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="userDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveUser">确定</el-button>
                </div>
            </el-dialog>
        </div>
    </script>

    <!-- 系统设置页面模板 -->
    <script type="text/x-template" id="settings-template">
        <div class="settings-container">
            <!-- 设置导航标签页 -->
            <el-tabs v-model="activeTab" type="border-card">
                <!-- 用户管理 -->
                <el-tab-pane label="用户管理" name="users">
                    <div class="settings-section">
                        <div class="section-header">
                            <div class="section-title">用户管理</div>
                            <el-button type="primary" size="small" icon="el-icon-plus" @click="addUser">添加用户</el-button>
                        </div>

                        <!-- 用户搜索和筛选 -->
                        <div class="filter-bar">
                            <el-input
                                placeholder="搜索用户名或姓名"
                                v-model="userSearchQuery"
                                style="width: 200px; margin-right: 10px;"
                                size="small"
                                prefix-icon="el-icon-search">
                            </el-input>
                            <el-select v-model="userRoleFilter" placeholder="角色" size="small" style="width: 120px; margin-right: 10px;">
                                <el-option label="全部角色" value=""></el-option>
                                <el-option label="管理员" value="admin"></el-option>
                                <el-option label="操作员" value="operator"></el-option>
                                <el-option label="观察员" value="viewer"></el-option>
                            </el-select>
                            <el-select v-model="userStatusFilter" placeholder="状态" size="small" style="width: 100px;">
                                <el-option label="全部状态" value=""></el-option>
                                <el-option label="启用" value="active"></el-option>
                                <el-option label="禁用" value="disabled"></el-option>
                            </el-select>
                        </div>

                        <!-- 用户列表 -->
                        <el-table :data="filteredUsers" style="width: 100%">
                            <el-table-column prop="id" label="用户ID" width="80"></el-table-column>
                            <el-table-column prop="username" label="用户名" width="120"></el-table-column>
                            <el-table-column prop="realName" label="真实姓名" width="120"></el-table-column>
                            <el-table-column prop="email" label="邮箱" width="180"></el-table-column>
                            <el-table-column prop="role" label="角色" width="100">
                                <template slot-scope="scope">
                                    <el-tag :type="getRoleType(scope.row.role)">{{ getRoleText(scope.row.role) }}</el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="status" label="状态" width="80">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.status === 'active' ? 'success' : 'danger'">
                                        {{ scope.row.status === 'active' ? '启用' : '禁用' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="lastLogin" label="最后登录" width="150"></el-table-column>
                            <el-table-column prop="createTime" label="创建时间" width="150"></el-table-column>
                            <el-table-column label="操作" width="200">
                                <template slot-scope="scope">
                                    <el-button type="text" size="small" @click="editUser(scope.row)">编辑</el-button>
                                    <el-button type="text" size="small" @click="resetPassword(scope.row)">重置密码</el-button>
                                    <el-button type="text" size="small" @click="toggleUserStatus(scope.row)">
                                        {{ scope.row.status === 'active' ? '禁用' : '启用' }}
                                    </el-button>
                                    <el-button type="text" size="small" style="color: #F56C6C;" @click="deleteUser(scope.row)">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>

                        <!-- 分页 -->
                        <el-pagination
                            @size-change="handleUserSizeChange"
                            @current-change="handleUserCurrentChange"
                            :current-page="userCurrentPage"
                            :page-sizes="[10, 20, 50, 100]"
                            :page-size="userPageSize"
                            layout="total, sizes, prev, pager, next, jumper"
                            :total="users.length"
                            style="margin-top: 20px; text-align: right;">
                        </el-pagination>
                    </div>
                </el-tab-pane>

                <!-- 角色权限 -->
                <el-tab-pane label="角色权限" name="roles">
                    <div class="settings-section">
                        <div class="section-header">
                            <div class="section-title">角色权限管理</div>
                            <el-button type="primary" size="small" icon="el-icon-plus" @click="addRole">添加角色</el-button>
                        </div>

                        <!-- 角色列表 -->
                        <el-table :data="roles" style="width: 100%">
                            <el-table-column prop="id" label="角色ID" width="80"></el-table-column>
                            <el-table-column prop="name" label="角色名称" width="120"></el-table-column>
                            <el-table-column prop="description" label="角色描述" width="200"></el-table-column>
                            <el-table-column prop="userCount" label="用户数量" width="100"></el-table-column>
                            <el-table-column label="权限" min-width="300">
                                <template slot-scope="scope">
                                    <el-tag v-for="permission in scope.row.permissions" :key="permission" size="small" style="margin-right: 5px;">
                                        {{ getPermissionText(permission) }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="createTime" label="创建时间" width="150"></el-table-column>
                            <el-table-column label="操作" width="150">
                                <template slot-scope="scope">
                                    <el-button type="text" size="small" @click="editRole(scope.row)">编辑</el-button>
                                    <el-button type="text" size="small" @click="editRolePermissions(scope.row)">权限</el-button>
                                    <el-button type="text" size="small" style="color: #F56C6C;" @click="deleteRole(scope.row)" :disabled="scope.row.isSystem">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>

                <!-- 系统配置 -->
                <el-tab-pane label="系统配置" name="system">
                    <div class="settings-section">
                        <div class="section-header">
                            <div class="section-title">系统参数配置</div>
                            <el-button type="success" size="small" icon="el-icon-check" @click="saveSystemConfig">保存配置</el-button>
                        </div>

                        <el-form :model="systemConfig" label-width="150px">
                            <el-card class="config-card">
                                <div slot="header">基础配置</div>
                                <el-form-item label="系统名称">
                                    <el-input v-model="systemConfig.systemName" placeholder="请输入系统名称"></el-input>
                                </el-form-item>
                                <el-form-item label="系统版本">
                                    <el-input v-model="systemConfig.systemVersion" placeholder="请输入系统版本"></el-input>
                                </el-form-item>
                                <el-form-item label="会话超时时间">
                                    <el-input-number v-model="systemConfig.sessionTimeout" :min="5" :max="1440" placeholder="分钟"></el-input-number>
                                    <span style="margin-left: 10px; color: #909399;">分钟</span>
                                </el-form-item>
                                <el-form-item label="密码复杂度">
                                    <el-switch v-model="systemConfig.passwordComplexity" active-text="启用" inactive-text="禁用"></el-switch>
                                </el-form-item>
                            </el-card>

                            <el-card class="config-card">
                                <div slot="header">监控配置</div>
                                <el-form-item label="数据刷新间隔">
                                    <el-input-number v-model="systemConfig.dataRefreshInterval" :min="1" :max="60" placeholder="秒"></el-input-number>
                                    <span style="margin-left: 10px; color: #909399;">秒</span>
                                </el-form-item>
                                <el-form-item label="预警阈值设置">
                                    <el-row :gutter="20">
                                        <el-col :span="8">
                                            <el-form-item label="温度预警(°C)">
                                                <el-input-number v-model="systemConfig.thresholds.temperature" :min="0" :max="100"></el-input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="速度预警(km/h)">
                                                <el-input-number v-model="systemConfig.thresholds.speed" :min="0" :max="200"></el-input-number>
                                            </el-form-item>
                                        </el-col>
                                        <el-col :span="8">
                                            <el-form-item label="压力预警(MPa)">
                                                <el-input-number v-model="systemConfig.thresholds.pressure" :min="0" :max="10" :precision="1"></el-input-number>
                                            </el-form-item>
                                        </el-col>
                                    </el-row>
                                </el-form-item>
                            </el-card>

                            <el-card class="config-card">
                                <div slot="header">通知配置</div>
                                <el-form-item label="邮件通知">
                                    <el-switch v-model="systemConfig.notifications.email" active-text="启用" inactive-text="禁用"></el-switch>
                                </el-form-item>
                                <el-form-item label="短信通知">
                                    <el-switch v-model="systemConfig.notifications.sms" active-text="启用" inactive-text="禁用"></el-switch>
                                </el-form-item>
                                <el-form-item label="系统通知">
                                    <el-switch v-model="systemConfig.notifications.system" active-text="启用" inactive-text="禁用"></el-switch>
                                </el-form-item>
                                <el-form-item label="SMTP服务器" v-if="systemConfig.notifications.email">
                                    <el-input v-model="systemConfig.emailConfig.smtpServer" placeholder="请输入SMTP服务器地址"></el-input>
                                </el-form-item>
                                <el-form-item label="SMTP端口" v-if="systemConfig.notifications.email">
                                    <el-input-number v-model="systemConfig.emailConfig.smtpPort" :min="1" :max="65535"></el-input-number>
                                </el-form-item>
                            </el-card>
                        </el-form>
                    </div>
                </el-tab-pane>
            </el-tabs>

            <!-- 用户编辑对话框 -->
            <el-dialog :title="userDialogTitle" :visible.sync="userDialogVisible" width="600px">
                <el-form :model="userForm" :rules="userRules" ref="userForm" label-width="100px">
                    <el-form-item label="用户名" prop="username">
                        <el-input v-model="userForm.username" :disabled="userForm.id"></el-input>
                    </el-form-item>
                    <el-form-item label="真实姓名" prop="realName">
                        <el-input v-model="userForm.realName"></el-input>
                    </el-form-item>
                    <el-form-item label="邮箱" prop="email">
                        <el-input v-model="userForm.email"></el-input>
                    </el-form-item>
                    <el-form-item label="手机号" prop="phone">
                        <el-input v-model="userForm.phone"></el-input>
                    </el-form-item>
                    <el-form-item label="角色" prop="role">
                        <el-select v-model="userForm.role" placeholder="请选择角色">
                            <el-option label="管理员" value="admin"></el-option>
                            <el-option label="操作员" value="operator"></el-option>
                            <el-option label="观察员" value="viewer"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="状态" prop="status">
                        <el-radio-group v-model="userForm.status">
                            <el-radio label="active">启用</el-radio>
                            <el-radio label="disabled">禁用</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="密码" prop="password" v-if="!userForm.id">
                        <el-input v-model="userForm.password" type="password" placeholder="请输入密码"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="userDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveUser">确定</el-button>
                </div>
            </el-dialog>
        </div>
    </script>

    <!-- 引入JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue-router@3.5.3/dist/vue-router.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.10/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios@0.21.1/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <!-- 百度地图API -->
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=YOUR_BAIDU_MAP_AK"></script>
    <!-- 百度地图配置 -->
    <script src="js/baidu-map-config.js"></script>
    <script src="js/main.js"></script>
    <script>
        // 扩展Vue实例方法
        Vue.prototype.openMap = function() {
            window.open('map.html', '_blank');
        };
    </script>
</body>
</html> 