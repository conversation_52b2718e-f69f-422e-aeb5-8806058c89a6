# 百度地图API集成说明

## 概述

本系统已集成百度地图API，用于实现列车运行状态的实时地图显示和运行轨迹的可视化回放功能。

## 配置步骤

### 1. 获取百度地图API密钥

1. 访问百度地图开放平台：https://lbsyun.baidu.com/
2. 注册并登录账号
3. 创建应用，获取AK（Access Key）
4. 在 `index.html` 中替换 `YOUR_BAIDU_MAP_AK` 为您的实际AK

```html
<script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=您的百度地图AK"></script>
```

### 2. 配置地图参数

在 `js/baidu-map-config.js` 文件中可以配置：

- 默认地图中心点坐标
- 默认缩放级别
- 地铁线路颜色
- 地铁站点坐标数据

## 功能特性

### 列车运行状态地图

1. **实时列车位置显示**
   - 在地图上显示所有运行中的列车
   - 列车图标根据运行状态显示不同颜色
   - 点击列车图标查看详细信息

2. **地铁站点标记**
   - 显示主要地铁线路的站点位置
   - 站点标签显示站点名称

3. **自动刷新**
   - 支持自动刷新列车位置
   - 可手动刷新数据

### 列车运行轨迹地图

1. **轨迹线绘制**
   - 根据历史数据绘制列车运行轨迹
   - 不同线路使用不同颜色

2. **轨迹回放**
   - 支持轨迹动画回放
   - 可调节播放速度（0.5x - 4x）
   - 显示当前播放位置和信息

3. **起终点标记**
   - 清晰标示轨迹起点和终点
   - 当前播放位置实时更新

## 地图控制功能

### 基础控制
- 缩放控制
- 平移控制
- 地图类型切换
- 比例尺显示
- 鹰眼地图

### 自定义功能
- 全屏查看
- 实时/历史模式切换
- 地图跟随播放位置

## 数据格式

### 列车位置数据格式
```javascript
{
    trainId: "T1208",
    line: "1号线",
    status: "正常运行",
    position: {
        lng: 116.375,  // 经度
        lat: 39.915    // 纬度
    },
    speed: "60km/h",
    // 其他列车信息...
}
```

### 轨迹数据格式
```javascript
{
    time: "08:00:00",
    station: "西单站",
    status: "停车",
    coordinates: {
        lng: 116.375,
        lat: 39.915
    },
    // 其他轨迹信息...
}
```

## 自定义配置

### 修改地图样式
在 `css/main.css` 中可以自定义：
- 地图容器样式
- 控制按钮样式
- 信息窗口样式

### 添加新的地铁线路
在 `js/baidu-map-config.js` 的 `stations` 对象中添加新线路的站点坐标：

```javascript
stations: {
    '新线路': [
        { name: '站点1', lng: 经度, lat: 纬度 },
        { name: '站点2', lng: 经度, lat: 纬度 },
        // ...
    ]
}
```

### 自定义列车图标
修改 `BaiduMapUtils` 类中的 `createTrainIcon` 方法来自定义列车图标样式。

## 注意事项

1. **API配额限制**
   - 百度地图API有使用配额限制
   - 建议在生产环境中监控API调用量

2. **坐标系统**
   - 百度地图使用BD09坐标系
   - 如果数据来源使用其他坐标系，需要进行坐标转换

3. **网络依赖**
   - 地图功能需要网络连接
   - 建议提供离线模式或降级方案

4. **浏览器兼容性**
   - 支持现代浏览器
   - IE需要9.0及以上版本

## 故障排除

### 地图无法显示
1. 检查百度地图AK是否正确配置
2. 检查网络连接是否正常
3. 查看浏览器控制台错误信息

### 列车位置不准确
1. 检查坐标数据是否正确
2. 确认坐标系统是否为BD09
3. 验证数据更新频率

### 轨迹回放异常
1. 检查轨迹数据的时间序列是否正确
2. 确认坐标数据完整性
3. 检查播放控制逻辑

## 扩展功能建议

1. **热力图显示**
   - 显示客流密度热力图
   - 分析高峰时段分布

2. **路径规划**
   - 集成路径规划功能
   - 优化调度路线

3. **实时交通**
   - 显示实时交通状况
   - 影响因素分析

4. **3D地图**
   - 升级到3D地图显示
   - 更直观的空间展示

## 技术支持

如需技术支持，请联系：
- 百度地图开放平台：https://lbsyun.baidu.com/
- 开发文档：https://lbsyun.baidu.com/cms/jsapi/reference/jsapi_reference.html
