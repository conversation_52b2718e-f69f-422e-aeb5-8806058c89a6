<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 轨道交通运输列车运行监测系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        }
        
        body {
            background-color: #004ea2;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            background-image: linear-gradient(135deg, #004ea2 0%, #003b7a 100%);
            overflow: hidden;
        }
        
        .login-container {
            width: 100%;
            max-width: 400px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
            padding: 40px 30px;
            position: relative;
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-header h2 {
            font-size: 24px;
            color: #333;
            margin-bottom: 10px;
        }
        
        .login-header p {
            font-size: 14px;
            color: #666;
        }
        
        .login-form {
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-control {
            width: 100%;
            height: 45px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 0 15px;
            font-size: 14px;
            color: #333;
            transition: border-color 0.3s;
        }
        
        .form-control:focus {
            border-color: #004ea2;
            outline: none;
            box-shadow: 0 0 0 2px rgba(0, 78, 162, 0.2);
        }
        
        .input-icon {
            position: relative;
        }
        
        .input-icon i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 16px;
        }
        
        .input-icon input {
            padding-left: 40px;
        }
        
        .btn {
            display: block;
            width: 100%;
            height: 45px;
            background-color: #004ea2;
            color: #fff;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #003b7a;
        }
        
        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 15px;
            font-size: 14px;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
        }
        
        .remember-me input {
            margin-right: 5px;
        }
        
        .forget-password {
            color: #004ea2;
            text-decoration: none;
        }
        
        .forget-password:hover {
            text-decoration: underline;
        }
        
        .login-footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #999;
        }
        
        .login-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        
        .login-bg::before {
            content: '';
            position: absolute;
            bottom: -50px;
            right: -50px;
            width: 200px;
            height: 200px;
            background-color: rgba(0, 78, 162, 0.05);
            border-radius: 50%;
        }
        
        .login-bg::after {
            content: '';
            position: absolute;
            top: -30px;
            left: -30px;
            width: 120px;
            height: 120px;
            background-color: rgba(0, 78, 162, 0.05);
            border-radius: 50%;
        }
        
        .system-title {
            position: absolute;
            top: -80px;
            left: 0;
            width: 100%;
            text-align: center;
            color: #fff;
        }
        
        .system-title h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .system-title p {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .copyright {
            position: absolute;
            bottom: -40px;
            left: 0;
            width: 100%;
            text-align: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="system-title">
            <h1>轨道交通运输列车运行监测系统</h1>
            <p>Rail Transit Train Operation Monitoring System</p>
        </div>
        
        <div class="login-header">
            <h2>系统登录</h2>
        </div>
        
        <form class="login-form" id="loginForm">
            <div class="form-group">
                <div class="input-icon">
                    <i>👤</i>
                    <input type="text" class="form-control" id="username" placeholder="用户名" required>
                </div>
            </div>
            <div class="form-group">
                <div class="input-icon">
                    <i>🔒</i>
                    <input type="password" class="form-control" id="password" placeholder="密码" required>
                </div>
            </div>
            <button type="submit" class="btn" id="loginBtn">登录</button>
            
            <div class="login-options">
                <label class="remember-me">
                    <input type="checkbox" id="rememberMe"> 记住我
                </label>
                <a href="#" class="forget-password">忘记密码?</a>
            </div>
        </form>
        
        <div class="login-bg"></div>
        
        <div class="copyright">
           
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            
            loginForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                // 显示加载状态
                loginBtn.textContent = '登录中...';
                loginBtn.disabled = true;
                
                // 模拟登录请求
                setTimeout(() => {
                    // 登录成功，跳转到首页
                    if (username === 'admin' && password === 'admin') {
                        localStorage.setItem('user', JSON.stringify({
                            username: username,
                            role: 'admin'
                        }));
                        window.location.href = 'index.html';
                    } else {
                        // 登录失败
                        alert('用户名或密码错误');
                        loginBtn.textContent = '登录';
                        loginBtn.disabled = false;
                    }
                }, 1000);
            });
        });
    </script>
</body>
</html> 