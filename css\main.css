/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f0f2f5;
    color: #333;
}

/* 布局样式 */
#app {
    height: 100vh;
}

.el-container {
    height: 100%;
}

.el-aside {
    background-color: #001529;
    color: #fff;
    overflow: hidden;
    transition: width 0.3s;
}

.el-aside .logo {
    height: 60px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #002140;
}

.el-aside .logo img {
    height: 40px;
    margin-right: 10px;
}

.el-aside .logo h2 {
    color: #fff;
    font-size: 16px;
    white-space: nowrap;
}

.el-menu-vertical {
    border-right: none;
}

.el-header {
    background-color: #fff;
    color: #333;
    line-height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
    display: flex;
    align-items: center;
}

.header-left .el-button {
    margin-right: 15px;
    font-size: 18px;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-right .time {
    margin-right: 20px;
    font-size: 14px;
    color: #666;
}

.user-info {
    cursor: pointer;
    display: flex;
    align-items: center;
}

.el-main {
    padding: 20px;
    background-color: #f0f2f5;
    overflow-y: auto;
}

/* 卡片样式 */
.data-card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    margin-bottom: 20px;
    padding: 20px;
}

.card-header {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 15px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
}

/* 数据统计卡片 */
.stat-cards {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.stat-card {
    flex: 1;
    min-width: 200px;
    margin: 10px;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
}

.stat-info .value {
    font-size: 24px;
    font-weight: bold;
    line-height: 1.2;
}

.stat-info .title {
    font-size: 14px;
    color: #909399;
}

/* 图表容器 */
.chart-container {
    height: 400px;
    width: 100%;
}

/* 表格样式 */
.custom-table {
    margin: 20px 0;
}

/* 状态标签 */
.status-tag {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
}

.status-normal {
    background-color: #f0f9eb;
    color: #67c23a;
}

.status-warning {
    background-color: #fdf6ec;
    color: #e6a23c;
}

.status-danger {
    background-color: #fef0f0;
    color: #f56c6c;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .stat-cards {
        flex-direction: column;
    }
    
    .stat-card {
        width: calc(100% - 20px);
    }
}

/* 列车运行状态页面特殊样式 */
.train-status-map {
    height: 500px;
    margin-bottom: 20px;
}

.train-list {
    max-height: 400px;
    overflow-y: auto;
}

/* 故障预警页面特殊样式 */
.warning-level-high {
    color: #f56c6c;
    font-weight: bold;
}

.warning-level-medium {
    color: #e6a23c;
}

.warning-level-low {
    color: #409eff;
}

/* 登录页样式 */
.login-container {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1a237e, #0277bd);
}

.login-form {
    width: 400px;
    padding: 40px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.login-title {
    text-align: center;
    margin-bottom: 30px;
    color: #303133;
}

.login-footer {
    text-align: center;
    margin-top: 20px;
    color: #909399;
}

/* 安全评估页面样式 */
.safety-container {
    padding: 0;
}

.safety-score-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    height: 100%;
}

.score-title {
    font-size: 18px;
    margin-bottom: 20px;
    opacity: 0.9;
}

.score-value {
    position: relative;
    margin-bottom: 15px;
}

.score-number {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 36px;
    font-weight: bold;
}

.score-desc {
    font-size: 14px;
    opacity: 0.8;
}

.safety-indicators {
    padding: 20px;
}

.indicator-card {
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    height: 100%;
    transition: all 0.3s;
}

.indicator-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.indicator-title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 10px;
}

.indicator-value {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 10px;
}

.indicator-excellent {
    color: #67C23A;
}

.indicator-good {
    color: #95D475;
}

.indicator-normal {
    color: #E6A23C;
}

.indicator-warning {
    color: #F78989;
}

.indicator-danger {
    color: #F56C6C;
}

.safety-detail-card {
    background: #fff;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
}

.detail-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
}

.detail-score {
    font-size: 24px;
    font-weight: bold;
}

.deviation-normal {
    color: #67C23A;
}

.deviation-warning {
    color: #E6A23C;
}

.deviation-danger {
    color: #F56C6C;
}

/* 风险预警卡片样式 */
.risk-warning-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #ddd;
    transition: all 0.3s;
    height: 100%;
}

.risk-warning-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.risk-warning-card.risk-高 {
    border-left-color: #F56C6C;
    background: linear-gradient(135deg, #fff 0%, #fef0f0 100%);
}

.risk-warning-card.risk-中 {
    border-left-color: #E6A23C;
    background: linear-gradient(135deg, #fff 0%, #fdf6ec 100%);
}

.risk-warning-card.risk-低 {
    border-left-color: #409EFF;
    background: linear-gradient(135deg, #fff 0%, #ecf5ff 100%);
}

.risk-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.risk-type {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
}

.risk-count {
    font-size: 24px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 10px;
}

.risk-trend {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #909399;
    margin-bottom: 10px;
}

.risk-trend i {
    margin: 0 5px;
}

.risk-desc {
    font-size: 12px;
    color: #606266;
    line-height: 1.4;
}

.risk-analysis {
    margin-top: 20px;
}

.analysis-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 15px;
}

/* 数据概览卡片样式 */
.data-overview-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    border: 1px solid #ebeef5;
    transition: all 0.3s;
}

.data-overview-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.overview-title {
    font-size: 14px;
    color: #909399;
    margin-bottom: 10px;
}

.overview-value {
    font-size: 28px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 8px;
}

.overview-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.overview-change.positive {
    color: #67C23A;
}

.overview-change.negative {
    color: #F56C6C;
}

.overview-change i {
    margin-right: 4px;
}

/* 图表卡片样式 */
.chart-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #ebeef5;
}

.chart-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 15px;
    text-align: center;
}

.safety-trend-chart {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #ebeef5;
}

/* 事件统计卡片样式 */
.incident-stat-card {
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s;
}

.incident-stat-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.incident-stat-card .stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 8px;
}

.incident-stat-card .stat-label {
    font-size: 14px;
    color: #909399;
}