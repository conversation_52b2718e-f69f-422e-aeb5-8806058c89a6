/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f0f2f5;
    color: #333;
}

/* 布局样式 */
#app {
    height: 100vh;
}

.el-container {
    height: 100%;
}

.el-aside {
    background-color: #001529;
    color: #fff;
    overflow: hidden;
    transition: width 0.3s;
}

.el-aside .logo {
    height: 60px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #002140;
}

.el-aside .logo img {
    height: 40px;
    margin-right: 10px;
}

.el-aside .logo h2 {
    color: #fff;
    font-size: 16px;
    white-space: nowrap;
}

.el-menu-vertical {
    border-right: none;
}

.el-header {
    background-color: #fff;
    color: #333;
    line-height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
    display: flex;
    align-items: center;
}

.header-left .el-button {
    margin-right: 15px;
    font-size: 18px;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-right .time {
    margin-right: 20px;
    font-size: 14px;
    color: #666;
}

.user-info {
    cursor: pointer;
    display: flex;
    align-items: center;
}

.el-main {
    padding: 20px;
    background-color: #f0f2f5;
    overflow-y: auto;
}

/* 卡片样式 */
.data-card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    margin-bottom: 20px;
    padding: 20px;
}

.card-header {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 15px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
}

/* 数据统计卡片 */
.stat-cards {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.stat-card {
    flex: 1;
    min-width: 200px;
    margin: 10px;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
}

.stat-info .value {
    font-size: 24px;
    font-weight: bold;
    line-height: 1.2;
}

.stat-info .title {
    font-size: 14px;
    color: #909399;
}

/* 图表容器 */
.chart-container {
    height: 400px;
    width: 100%;
}

/* 表格样式 */
.custom-table {
    margin: 20px 0;
}

/* 状态标签 */
.status-tag {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
}

.status-normal {
    background-color: #f0f9eb;
    color: #67c23a;
}

.status-warning {
    background-color: #fdf6ec;
    color: #e6a23c;
}

.status-danger {
    background-color: #fef0f0;
    color: #f56c6c;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .stat-cards {
        flex-direction: column;
    }
    
    .stat-card {
        width: calc(100% - 20px);
    }
}

/* 列车运行状态页面特殊样式 */
.train-status-map {
    height: 500px;
    margin-bottom: 20px;
}

.train-list {
    max-height: 400px;
    overflow-y: auto;
}

/* 故障预警页面特殊样式 */
.warning-level-high {
    color: #f56c6c;
    font-weight: bold;
}

.warning-level-medium {
    color: #e6a23c;
}

.warning-level-low {
    color: #409eff;
}

/* 登录页样式 */
.login-container {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1a237e, #0277bd);
}

.login-form {
    width: 400px;
    padding: 40px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.login-title {
    text-align: center;
    margin-bottom: 30px;
    color: #303133;
}

.login-footer {
    text-align: center;
    margin-top: 20px;
    color: #909399;
}

/* 安全评估页面样式 */
.safety-container {
    padding: 0;
}

.safety-score-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    height: 100%;
}

.score-title {
    font-size: 18px;
    margin-bottom: 20px;
    opacity: 0.9;
}

.score-value {
    position: relative;
    margin-bottom: 15px;
}

.score-number {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 36px;
    font-weight: bold;
}

.score-desc {
    font-size: 14px;
    opacity: 0.8;
}

.safety-indicators {
    padding: 20px;
}

.indicator-card {
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    height: 100%;
    transition: all 0.3s;
}

.indicator-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.indicator-title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 10px;
}

.indicator-value {
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 10px;
}

.indicator-excellent {
    color: #67C23A;
}

.indicator-good {
    color: #95D475;
}

.indicator-normal {
    color: #E6A23C;
}

.indicator-warning {
    color: #F78989;
}

.indicator-danger {
    color: #F56C6C;
}

.safety-detail-card {
    background: #fff;
    border-radius: 6px;
    padding: 20px;
    margin-bottom: 20px;
}

.detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
}

.detail-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
}

.detail-score {
    font-size: 24px;
    font-weight: bold;
}

.deviation-normal {
    color: #67C23A;
}

.deviation-warning {
    color: #E6A23C;
}

.deviation-danger {
    color: #F56C6C;
}

/* 风险预警卡片样式 */
.risk-warning-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    border-left: 4px solid #ddd;
    transition: all 0.3s;
    height: 100%;
}

.risk-warning-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.risk-warning-card.risk-高 {
    border-left-color: #F56C6C;
    background: linear-gradient(135deg, #fff 0%, #fef0f0 100%);
}

.risk-warning-card.risk-中 {
    border-left-color: #E6A23C;
    background: linear-gradient(135deg, #fff 0%, #fdf6ec 100%);
}

.risk-warning-card.risk-低 {
    border-left-color: #409EFF;
    background: linear-gradient(135deg, #fff 0%, #ecf5ff 100%);
}

.risk-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.risk-type {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
}

.risk-count {
    font-size: 24px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 10px;
}

.risk-trend {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #909399;
    margin-bottom: 10px;
}

.risk-trend i {
    margin: 0 5px;
}

.risk-desc {
    font-size: 12px;
    color: #606266;
    line-height: 1.4;
}

.risk-analysis {
    margin-top: 20px;
}

.analysis-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 15px;
}

/* 数据概览卡片样式 */
.data-overview-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    border: 1px solid #ebeef5;
    transition: all 0.3s;
}

.data-overview-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.overview-title {
    font-size: 14px;
    color: #909399;
    margin-bottom: 10px;
}

.overview-value {
    font-size: 28px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 8px;
}

.overview-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.overview-change.positive {
    color: #67C23A;
}

.overview-change.negative {
    color: #F56C6C;
}

.overview-change i {
    margin-right: 4px;
}

/* 图表卡片样式 */
.chart-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #ebeef5;
}

.chart-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 15px;
    text-align: center;
}

.safety-trend-chart {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #ebeef5;
}

/* 事件统计卡片样式 */
.incident-stat-card {
    background: #fff;
    border: 1px solid #ebeef5;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s;
}

.incident-stat-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.incident-stat-card .stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 8px;
}

.incident-stat-card .stat-label {
    font-size: 14px;
    color: #909399;
}

/* 数据统计页面样式 */
.statistics-container {
    padding: 0;
}

/* 核心指标卡片样式 */
.metric-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    border: 1px solid #ebeef5;
    transition: all 0.3s;
    height: 100%;
}

.metric-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.metric-card.metric-passenger {
    border-left: 4px solid #409EFF;
}

.metric-card.metric-revenue {
    border-left: 4px solid #67C23A;
}

.metric-card.metric-efficiency {
    border-left: 4px solid #E6A23C;
}

.metric-card.metric-utilization {
    border-left: 4px solid #F56C6C;
}

.metric-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: #fff;
}

.metric-passenger .metric-icon {
    background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.metric-revenue .metric-icon {
    background: linear-gradient(135deg, #67C23A, #95D475);
}

.metric-efficiency .metric-icon {
    background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.metric-utilization .metric-icon {
    background: linear-gradient(135deg, #F56C6C, #F78989);
}

.metric-content {
    flex: 1;
}

.metric-value {
    font-size: 28px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 5px;
}

.metric-label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 5px;
}

.metric-change {
    font-size: 12px;
    display: flex;
    align-items: center;
}

.metric-change.positive {
    color: #67C23A;
}

.metric-change.negative {
    color: #F56C6C;
}

.metric-change i {
    margin-right: 4px;
}

/* 收入汇总卡片样式 */
.revenue-summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 25px;
    border-radius: 8px;
    text-align: center;
    height: 100%;
}

.summary-title {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 10px;
}

.summary-value {
    font-size: 32px;
    font-weight: bold;
    margin-bottom: 8px;
}

.summary-change {
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.summary-change i {
    margin-right: 4px;
}

.summary-change.positive {
    color: #95D475;
}

.summary-change.negative {
    color: #F78989;
}

/* 图表卡片增强样式 */
.chart-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    border: 1px solid #ebeef5;
    height: 100%;
}

.chart-card .chart-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 15px;
    text-align: center;
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
}

/* 标签页样式优化 */
.el-tabs--border-card {
    border: 1px solid #ebeef5;
    border-radius: 8px;
    overflow: hidden;
}

.el-tabs--border-card > .el-tabs__header {
    background-color: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
}

.el-tabs--border-card > .el-tabs__content {
    padding: 20px;
}

/* 表格样式优化 */
.el-table {
    border-radius: 6px;
    overflow: hidden;
}

.el-table th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: 500;
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
    .metric-card {
        margin-bottom: 15px;
    }

    .chart-container {
        height: 300px;
    }
}

@media screen and (max-width: 768px) {
    .metric-card {
        flex-direction: column;
        text-align: center;
    }

    .metric-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .chart-container {
        height: 250px;
    }
}

/* 列车运行状态页面样式 */
.train-status-container {
    padding: 0;
}

/* 状态统计卡片样式 */
.status-stat-card {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    border: 1px solid #ebeef5;
    transition: all 0.3s;
    height: 100%;
}

.status-stat-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.status-stat-card.stat-total {
    border-left: 4px solid #409EFF;
}

.status-stat-card.stat-normal {
    border-left: 4px solid #67C23A;
}

.status-stat-card.stat-warning {
    border-left: 4px solid #E6A23C;
}

.status-stat-card.stat-speed {
    border-left: 4px solid #F56C6C;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    color: #fff;
}

.stat-total .stat-icon {
    background: linear-gradient(135deg, #409EFF, #66B1FF);
}

.stat-normal .stat-icon {
    background: linear-gradient(135deg, #67C23A, #95D475);
}

.stat-warning .stat-icon {
    background: linear-gradient(135deg, #E6A23C, #EEBE77);
}

.stat-speed .stat-icon {
    background: linear-gradient(135deg, #F56C6C, #F78989);
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 28px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 14px;
    color: #606266;
    margin-bottom: 5px;
}

.stat-change {
    font-size: 12px;
    display: flex;
    align-items: center;
}

.stat-change.positive {
    color: #67C23A;
}

.stat-change.negative {
    color: #F56C6C;
}

.stat-change.stable {
    color: #909399;
}

.stat-change i {
    margin-right: 4px;
}

/* 实时监控样式 */
.realtime-monitor {
    padding: 10px 0;
}

.monitor-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.monitor-item:last-child {
    border-bottom: none;
}

.monitor-label {
    font-size: 14px;
    color: #606266;
    flex: 1;
}

.monitor-value {
    font-size: 16px;
    font-weight: 500;
    margin-right: 10px;
}

.monitor-value.normal {
    color: #67C23A;
}

.monitor-value.good {
    color: #409EFF;
}

.monitor-value.warning {
    color: #E6A23C;
}

.monitor-value.danger {
    color: #F56C6C;
}

.monitor-trend {
    font-size: 12px;
    color: #909399;
    display: flex;
    align-items: center;
}

.monitor-trend i {
    margin-right: 2px;
}

/* 列车地图样式 */
.train-status-map {
    background: #f5f7fa;
    border-radius: 4px;
    min-height: 400px;
}

/* 列车状态标签样式 */
.status-tag {
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.status-normal {
    background: #f0f9ff;
    color: #67C23A;
    border: 1px solid #b3e19d;
}

.status-warning {
    background: #fdf6ec;
    color: #E6A23C;
    border: 1px solid #f5dab1;
}

.status-danger {
    background: #fef0f0;
    color: #F56C6C;
    border: 1px solid #fbc4c4;
}

/* 列车详情对话框样式 */
.el-descriptions {
    margin-bottom: 20px;
}

.el-descriptions__label {
    font-weight: 500;
}

/* 过滤栏样式 */
.filter-bar {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
    .status-stat-card {
        margin-bottom: 15px;
    }

    .realtime-monitor {
        margin-top: 20px;
    }
}

@media screen and (max-width: 768px) {
    .status-stat-card {
        flex-direction: column;
        text-align: center;
    }

    .stat-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .filter-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-bar > * {
        margin-bottom: 10px;
        margin-right: 0 !important;
    }
}

/* 列车运行轨迹页面样式 */
.train-track-container {
    padding: 0;
}

/* 轨迹控制面板样式 */
.track-controls {
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 0;
}

.track-controls .el-form-item {
    margin-bottom: 0;
}

.track-controls .el-form-item__label {
    font-size: 14px;
    font-weight: 500;
    color: #606266;
}

/* 轨迹地图样式 */
.train-track-map {
    background: #f5f7fa;
    border-radius: 4px;
    min-height: 500px;
}

/* 轨迹播放控制器样式 */
.track-player {
    background: #fff;
    border-top: 1px solid #ebeef5;
    padding: 15px 20px;
}

.player-controls {
    display: flex;
    align-items: center;
    gap: 15px;
}

.player-progress {
    flex: 1;
    display: flex;
    align-items: center;
}

.player-speed {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;
}

.player-info {
    min-width: 200px;
    font-size: 14px;
    color: #409EFF;
    font-weight: 500;
}

/* 轨迹统计样式 */
.track-statistics {
    padding: 15px 0;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-label {
    font-size: 14px;
    color: #606266;
}

.stat-value {
    font-size: 16px;
    font-weight: 500;
}

/* 轨迹分析样式 */
.track-analysis {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ebeef5;
}

.analysis-title {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 10px;
}

/* 轨迹播放进度条样式 */
.el-slider__runway {
    background-color: #e4e7ed;
}

.el-slider__bar {
    background-color: #409EFF;
}

.el-slider__button {
    border: 2px solid #409EFF;
    background-color: #fff;
}

/* 轨迹表格行点击效果 */
.el-table__row {
    cursor: pointer;
    transition: background-color 0.3s;
}

.el-table__row:hover {
    background-color: #f5f7fa !important;
}

/* 轨迹状态标签样式 */
.track-status-normal {
    background: #f0f9ff;
    color: #67C23A;
    border: 1px solid #b3e19d;
}

.track-status-station {
    background: #fdf6ec;
    color: #E6A23C;
    border: 1px solid #f5dab1;
}

.track-status-stop {
    background: #f4f4f5;
    color: #909399;
    border: 1px solid #d3d4d6;
}

/* 播放控制按钮样式 */
.player-controls .el-button-group .el-button {
    padding: 8px 12px;
}

.player-controls .el-button-group .el-button i {
    font-size: 14px;
}

/* 轨迹地图容器样式 */
#track-map {
    border-radius: 4px;
    overflow: hidden;
}

/* 速度分析图表容器样式 */
#speed-analysis-chart {
    border-radius: 4px;
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
    .track-controls .el-col {
        margin-bottom: 15px;
    }

    .player-controls {
        flex-direction: column;
        gap: 10px;
    }

    .player-progress {
        width: 100%;
        order: 3;
    }

    .player-info {
        min-width: auto;
        text-align: center;
        order: 2;
    }
}

@media screen and (max-width: 768px) {
    .track-controls {
        padding: 15px;
    }

    .track-controls .el-form-item__label {
        font-size: 12px;
    }

    .player-controls .el-button-group {
        width: 100%;
    }

    .player-controls .el-button {
        flex: 1;
        font-size: 12px;
    }

    .player-speed {
        flex-direction: column;
        gap: 5px;
    }

    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}