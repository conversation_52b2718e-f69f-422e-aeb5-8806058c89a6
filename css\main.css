/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f0f2f5;
    color: #333;
}

/* 布局样式 */
#app {
    height: 100vh;
}

.el-container {
    height: 100%;
}

.el-aside {
    background-color: #001529;
    color: #fff;
    overflow: hidden;
    transition: width 0.3s;
}

.el-aside .logo {
    height: 60px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #002140;
}

.el-aside .logo img {
    height: 40px;
    margin-right: 10px;
}

.el-aside .logo h2 {
    color: #fff;
    font-size: 16px;
    white-space: nowrap;
}

.el-menu-vertical {
    border-right: none;
}

.el-header {
    background-color: #fff;
    color: #333;
    line-height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
    display: flex;
    align-items: center;
}

.header-left .el-button {
    margin-right: 15px;
    font-size: 18px;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-right .time {
    margin-right: 20px;
    font-size: 14px;
    color: #666;
}

.user-info {
    cursor: pointer;
    display: flex;
    align-items: center;
}

.el-main {
    padding: 20px;
    background-color: #f0f2f5;
    overflow-y: auto;
}

/* 卡片样式 */
.data-card {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    margin-bottom: 20px;
    padding: 20px;
}

.card-header {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 15px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
}

/* 数据统计卡片 */
.stat-cards {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.stat-card {
    flex: 1;
    min-width: 200px;
    margin: 10px;
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
}

.stat-info .value {
    font-size: 24px;
    font-weight: bold;
    line-height: 1.2;
}

.stat-info .title {
    font-size: 14px;
    color: #909399;
}

/* 图表容器 */
.chart-container {
    height: 400px;
    width: 100%;
}

/* 表格样式 */
.custom-table {
    margin: 20px 0;
}

/* 状态标签 */
.status-tag {
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 12px;
}

.status-normal {
    background-color: #f0f9eb;
    color: #67c23a;
}

.status-warning {
    background-color: #fdf6ec;
    color: #e6a23c;
}

.status-danger {
    background-color: #fef0f0;
    color: #f56c6c;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
    .stat-cards {
        flex-direction: column;
    }
    
    .stat-card {
        width: calc(100% - 20px);
    }
}

/* 列车运行状态页面特殊样式 */
.train-status-map {
    height: 500px;
    margin-bottom: 20px;
}

.train-list {
    max-height: 400px;
    overflow-y: auto;
}

/* 故障预警页面特殊样式 */
.warning-level-high {
    color: #f56c6c;
    font-weight: bold;
}

.warning-level-medium {
    color: #e6a23c;
}

.warning-level-low {
    color: #409eff;
}

/* 登录页样式 */
.login-container {
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #1a237e, #0277bd);
}

.login-form {
    width: 400px;
    padding: 40px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.login-title {
    text-align: center;
    margin-bottom: 30px;
    color: #303133;
}

.login-footer {
    text-align: center;
    margin-top: 20px;
    color: #909399;
} 