<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>列车运行地图 - 轨道交通运输列车运行监测系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background-color: #004ea2;
            color: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            margin-right: 15px;
            cursor: pointer;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
        }
        
        .breadcrumb a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }
        
        .breadcrumb span {
            margin: 0 8px;
            color: rgba(255, 255, 255, 0.6);
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .time {
            margin-right: 20px;
            font-size: 14px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .user-info i {
            margin-left: 5px;
        }
        
        .container {
            padding: 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: bold;
        }
        
        .refresh-btn {
            background-color: #009eff;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
        
        .refresh-btn i {
            margin-right: 5px;
        }
        
        .map-container {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 15px;
            margin-bottom: 20px;
        }
        
        #map {
            width: 100%;
            height: 600px;
            border: 1px solid #eee;
        }
        
        .train-list {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 15px;
        }
        
        .list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .list-title {
            font-size: 16px;
            font-weight: bold;
        }
        
        .filter-group {
            display: flex;
            gap: 10px;
        }
        
        .filter-input {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .filter-select {
            padding: 6px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: #f9f9f9;
            font-weight: bold;
        }
        
        .status-normal {
            color: #52c41a;
        }
        
        .status-warning {
            color: #faad14;
        }
        
        .status-danger {
            color: #f5222d;
        }
        
        .action-btn {
            color: #1890ff;
            margin-right: 10px;
            text-decoration: none;
            cursor: pointer;
        }
        
        .map-placeholder {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100%;
            color: #999;
        }
        
        .map-placeholder i {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        /* 地图标记样式 */
        .train-marker {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }
        
        .train-marker.normal {
            background-color: #52c41a;
        }
        
        .train-marker.warning {
            background-color: #faad14;
        }
        
        .train-marker.danger {
            background-color: #f5222d;
        }
        
        .train-info-window {
            padding: 10px;
            width: 240px;
        }
        
        .train-info-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .train-info-content {
            font-size: 13px;
            line-height: 1.5;
        }
        
        .train-info-row {
            display: flex;
            margin-bottom: 5px;
        }
        
        .train-info-label {
            width: 70px;
            color: #666;
        }

        /* 百度地图信息窗口样式覆盖 */
        .BMap_bubble_title {
            font-weight: bold;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
            margin-bottom: 8px;
        }
        
        .BMap_bubble_content {
            padding: 5px 0;
        }

        .BMap_pop div:nth-child(9) {
            border-radius: 4px !important;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <button class="menu-btn">☰</button>
            <div class="breadcrumb">
                <a href="index.html">首页</a>
                <span>/</span>
                <a href="#">列车运行状态</a>
            </div>
        </div>
        <div class="header-right">
            <div class="time">2025-07-19 10:32:03</div>
            <div class="user-info">
                管理员 ▼
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="page-header">
            <div class="page-title">列车运行地图</div>
            <button class="refresh-btn" id="refreshBtn">
                <i>⟳</i> 刷新数据
            </button>
        </div>
        
        <div class="map-container">
            <div id="map"></div>
        </div>
        
        <div class="train-list">
            <div class="list-header">
                <div class="list-title">列车运行列表</div>
                <div class="filter-group">
                    <input type="text" class="filter-input" placeholder="搜索列车号或线路" id="searchInput">
                    <select class="filter-select" id="lineFilter">
                        <option value="">全部线路</option>
                        <option value="1号线">1号线</option>
                        <option value="2号线">2号线</option>
                        <option value="4号线">4号线</option>
                        <option value="5号线">5号线</option>
                        <option value="6号线">6号线</option>
                    </select>
                    <select class="filter-select" id="statusFilter">
                        <option value="">全部状态</option>
                        <option value="正常运行">正常运行</option>
                        <option value="减速运行">减速运行</option>
                        <option value="临时停车">临时停车</option>
                    </select>
                </div>
            </div>
            
            <table id="trainTable">
                <thead>
                    <tr>
                        <th>列车号</th>
                        <th>线路</th>
                        <th>状态</th>
                        <th>速度</th>
                        <th>当前位置</th>
                        <th>下一站</th>
                        <th>预计到达</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- 表格数据将由JavaScript动态生成 -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- 引入百度地图API -->
    <script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=8yVXQpKiWKWOSEMUzI7WEGhFAlRv9qmy"></script>
    <script>
        // 模拟列车数据
        const trainData = [
            { 
                trainId: 'T1208', 
                line: '1号线', 
                status: '正常运行', 
                speed: '60km/h', 
                position: '西单站-复兴门站', 
                nextStation: '复兴门站', 
                arrivalTime: '2分钟',
                location: [116.373, 39.915], // 北京西单站附近坐标
                direction: 90 // 向东
            },
            { 
                trainId: 'T0537', 
                line: '2号线', 
                status: '减速运行', 
                speed: '45km/h', 
                position: '朝阳门站-东四十条站', 
                nextStation: '东四十条站', 
                arrivalTime: '4分钟',
                location: [116.435, 39.928], // 北京朝阳门站附近坐标
                direction: 45 // 东北方向
            },
            { 
                trainId: 'T0912', 
                line: '4号线', 
                status: '正常运行', 
                speed: '58km/h', 
                position: '北京南站-陶然亭站', 
                nextStation: '陶然亭站', 
                arrivalTime: '3分钟',
                location: [116.378, 39.865], // 北京南站附近坐标
                direction: 0 // 向北
            },
            { 
                trainId: 'T0315', 
                line: '5号线', 
                status: '正常运行', 
                speed: '62km/h', 
                position: '惠新西街南口站-和平西桥站', 
                nextStation: '和平西桥站', 
                arrivalTime: '2分钟',
                location: [116.417, 39.972], // 惠新西街南口站附近坐标
                direction: 270 // 向西
            },
            { 
                trainId: 'T0721', 
                line: '6号线', 
                status: '临时停车', 
                speed: '0km/h', 
                position: '草房站', 
                nextStation: '常营站', 
                arrivalTime: '未知',
                location: [116.553, 39.923], // 草房站附近坐标
                direction: 90 // 向东
            }
        ];

        // 初始化地图
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否加载了百度地图API
            if (typeof BMap === 'undefined') {
                // 如果API未加载，显示占位内容
                document.getElementById('map').innerHTML = `
                    <div class="map-placeholder">
                        <i>🗺️</i>
                        <p>列车运行地图将在此显示</p>
                        <p style="font-size: 12px; margin-top: 10px;">请确保已配置正确的百度地图API密钥</p>
                    </div>
                `;
                
                // 仍然加载列车列表
                renderTrainList(trainData);
                setupFilters();
                return;
            }

            // 创建百度地图实例
            const map = new BMap.Map("map");
            
            // 设置地图中心点和缩放级别
            const centerPoint = new BMap.Point(116.404, 39.915); // 北京市中心
            map.centerAndZoom(centerPoint, 12);
            
            // 启用地图缩放和平移
            map.enableScrollWheelZoom();
            map.enableDragging();
            
            // 添加地图控件
            map.addControl(new BMap.NavigationControl()); // 添加平移缩放控件
            map.addControl(new BMap.ScaleControl());      // 添加比例尺控件
            map.addControl(new BMap.OverviewMapControl()); // 添加缩略图控件
            map.addControl(new BMap.MapTypeControl());    // 添加地图类型控件

            // 添加列车标记
            trainData.forEach(train => {
                // 坐标转换（WGS84坐标转换为百度坐标）
                const point = new BMap.Point(train.location[0], train.location[1]);
                
                // 创建自定义标记图标
                const markerIcon = new BMap.Symbol(BMap_Symbol_SHAPE_CIRCLE, {
                    scale: 5,
                    fillColor: getStatusColor(train.status),
                    fillOpacity: 0.8,
                    strokeColor: '#fff',
                    strokeWeight: 2
                });
                
                // 创建标记
                const marker = new BMap.Marker(point, {
                    icon: markerIcon,
                    rotation: train.direction,
                    title: `${train.trainId} (${train.line})`
                });
                
                // 创建信息窗口内容
                const infoWindowContent = `
                    <div class="train-info-window">
                        <div class="train-info-title">${train.trainId} (${train.line})</div>
                        <div class="train-info-content">
                            <div class="train-info-row">
                                <div class="train-info-label">状态:</div>
                                <div>${train.status}</div>
                            </div>
                            <div class="train-info-row">
                                <div class="train-info-label">速度:</div>
                                <div>${train.speed}</div>
                            </div>
                            <div class="train-info-row">
                                <div class="train-info-label">当前位置:</div>
                                <div>${train.position}</div>
                            </div>
                            <div class="train-info-row">
                                <div class="train-info-label">下一站:</div>
                                <div>${train.nextStation} (${train.arrivalTime})</div>
                            </div>
                        </div>
                    </div>
                `;
                
                // 创建信息窗口
                const infoWindow = new BMap.InfoWindow(infoWindowContent, {
                    width: 260,
                    height: 160,
                    title: `${train.trainId} (${train.line})`,
                    enableMessage: false
                });
                
                // 点击标记时显示信息窗口
                marker.addEventListener('click', function() {
                    map.openInfoWindow(infoWindow, point);
                });
                
                // 将标记添加到地图
                map.addOverlay(marker);
                
                // 创建列车图标标记（可选）
                const trainIconUrl = getTrainIconByStatus(train.status);
                const trainIcon = new BMap.Icon(trainIconUrl, new BMap.Size(24, 24), {
                    imageSize: new BMap.Size(24, 24),
                    anchor: new BMap.Size(12, 12)
                });
                
                const trainMarker = new BMap.Marker(point, {
                    icon: trainIcon,
                    rotation: train.direction
                });
                
                // 将列车图标标记添加到地图
                map.addOverlay(trainMarker);
            });

            // 渲染列车列表
            renderTrainList(trainData);
            
            // 设置过滤功能
            setupFilters();
            
            // 刷新按钮点击事件
            document.getElementById('refreshBtn').addEventListener('click', function() {
                alert('数据已更新');
                // 实际应用中，这里应该重新获取列车数据并更新地图和列表
            });
        });
        
        // 根据列车状态获取颜色
        function getStatusColor(status) {
            if (status === '正常运行') return '#52c41a'; // 绿色
            if (status === '减速运行') return '#faad14'; // 黄色
            if (status === '临时停车') return '#f5222d'; // 红色
            return '#1890ff'; // 蓝色（默认）
        }
        
        // 根据列车状态获取图标URL
        function getTrainIconByStatus(status) {
            // 实际应用中，可以使用不同颜色的列车图标
            // 这里使用了Base64编码的SVG图标，也可以使用外部图片URL
            return 'data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iIzMzMyIgZD0iTTEyLDVDOC4xMyw1LDUsOC4xMyw1LDEySDE5QzE5LDguMTMsMTUuODcsNSwxMiw1TTYsMTNDNSwxMyw1LDE0LDYsMTRIMThDMTksMTQsMTksMTMsMTgsMTNINk0xOSwxNUg1VjE3QTIsMiAwIDAsMCA3LDE5SDE3QTIsMiAwIDAsMCAxOSwxN1YxNU05LDE3VjE5SDExVjE3SDlNMTMsMTdWMTlIMTVWMTdIMTNaIiAvPjwvc3ZnPg==';
        }
        
        // 渲染列车列表
        function renderTrainList(trains) {
            const tableBody = document.querySelector('#trainTable tbody');
            tableBody.innerHTML = '';
            
            trains.forEach(train => {
                const row = document.createElement('tr');
                
                // 设置状态样式
                let statusClass = 'status-normal';
                if (train.status === '减速运行') {
                    statusClass = 'status-warning';
                } else if (train.status === '临时停车') {
                    statusClass = 'status-danger';
                }
                
                row.innerHTML = `
                    <td>${train.trainId}</td>
                    <td>${train.line}</td>
                    <td class="${statusClass}">${train.status}</td>
                    <td>${train.speed}</td>
                    <td>${train.position}</td>
                    <td>${train.nextStation}</td>
                    <td>${train.arrivalTime}</td>
                    <td>
                        <a class="action-btn" data-train-id="${train.trainId}">详情</a>
                        <a class="action-btn" data-train-id="${train.trainId}">轨迹</a>
                    </td>
                `;
                
                tableBody.appendChild(row);
            });
            
            // 添加操作按钮点击事件
            document.querySelectorAll('.action-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const trainId = this.getAttribute('data-train-id');
                    const action = this.textContent;
                    
                    if (action === '详情') {
                        alert(`查看列车 ${trainId} 详情`);
                    } else if (action === '轨迹') {
                        alert(`查看列车 ${trainId} 运行轨迹`);
                    }
                });
            });
        }
        
        // 设置过滤功能
        function setupFilters() {
            const searchInput = document.getElementById('searchInput');
            const lineFilter = document.getElementById('lineFilter');
            const statusFilter = document.getElementById('statusFilter');
            
            function filterTrains() {
                const searchTerm = searchInput.value.toLowerCase();
                const selectedLine = lineFilter.value;
                const selectedStatus = statusFilter.value;
                
                const filteredTrains = trainData.filter(train => {
                    const matchSearch = train.trainId.toLowerCase().includes(searchTerm) || 
                                       train.line.toLowerCase().includes(searchTerm);
                    const matchLine = selectedLine ? train.line === selectedLine : true;
                    const matchStatus = selectedStatus ? train.status === selectedStatus : true;
                    
                    return matchSearch && matchLine && matchStatus;
                });
                
                renderTrainList(filteredTrains);
            }
            
            searchInput.addEventListener('input', filterTrains);
            lineFilter.addEventListener('change', filterTrains);
            statusFilter.addEventListener('change', filterTrains);
        }
        
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            
            document.querySelector('.time').textContent = 
                `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
        
        // 每秒更新时间
        setInterval(updateTime, 1000);
        updateTime();
    </script>
</body>
</html> 