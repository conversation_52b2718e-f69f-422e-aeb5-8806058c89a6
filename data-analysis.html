<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据分析 - 轨道交通运输列车运行监测系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui@2.15.10/lib/theme-chalk/index.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
        }
        
        body {
            background-color: #f5f5f5;
            color: #333;
        }
        
        .header {
            background-color: #004ea2;
            color: white;
            padding: 0 20px;
            height: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .header-left {
            display: flex;
            align-items: center;
        }
        
        .menu-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            margin-right: 15px;
            cursor: pointer;
        }
        
        .breadcrumb {
            display: flex;
            align-items: center;
        }
        
        .breadcrumb a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
        }
        
        .breadcrumb span {
            margin: 0 8px;
            color: rgba(255, 255, 255, 0.6);
        }
        
        .header-right {
            display: flex;
            align-items: center;
        }
        
        .time {
            margin-right: 20px;
            font-size: 14px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            cursor: pointer;
        }
        
        .user-info i {
            margin-left: 5px;
        }
        
        .container {
            padding: 20px;
        }
        
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .page-title {
            font-size: 20px;
            font-weight: bold;
        }
        
        .filter-group {
            display: flex;
            gap: 10px;
        }
        
        .card {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: bold;
        }
        
        .chart-container {
            height: 350px;
        }
        
        .data-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .data-card {
            background-color: white;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 20px;
            display: flex;
            flex-direction: column;
        }
        
        .data-value {
            font-size: 28px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .data-title {
            font-size: 14px;
            color: #666;
        }
        
        .data-change {
            font-size: 14px;
            margin-top: auto;
        }
        
        .data-change.positive {
            color: #52c41a;
        }
        
        .data-change.negative {
            color: #f5222d;
        }
        
        .tabs {
            margin-bottom: 20px;
        }
        
        .tab {
            display: inline-block;
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            margin-right: 10px;
        }
        
        .tab.active {
            border-bottom-color: #004ea2;
            color: #004ea2;
            font-weight: bold;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background-color: #f9f9f9;
            font-weight: bold;
        }
        
        .pagination {
            display: flex;
            justify-content: flex-end;
            margin-top: 20px;
        }
        
        .pagination button {
            background-color: white;
            border: 1px solid #ddd;
            padding: 5px 10px;
            margin-left: 5px;
            cursor: pointer;
        }
        
        .pagination button.active {
            background-color: #004ea2;
            color: white;
            border-color: #004ea2;
        }
        
        .analysis-tools {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .tool-btn {
            padding: 8px 15px;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
        
        .tool-btn i {
            margin-right: 5px;
        }
        
        .tool-btn:hover {
            background-color: #f0f0f0;
        }
        
        .export-btn {
            background-color: #004ea2;
            color: white;
            border: none;
        }
        
        .export-btn:hover {
            background-color: #003b7a;
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid-2 {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <button class="menu-btn">☰</button>
            <div class="breadcrumb">
                <a href="index.html">首页</a>
                <span>/</span>
                <a href="#">数据分析</a>
            </div>
        </div>
        <div class="header-right">
            <div class="time">2023-07-19 10:32:03</div>
            <div class="user-info">
                管理员 ▼
            </div>
        </div>
    </div>
    
    <div class="container">
        <div class="page-header">
            <div class="page-title">运行数据分析</div>
            <div class="filter-group">
                <div id="date-picker" class="el-date-editor el-range-editor el-input__inner el-date-editor--daterange">
                    <i class="el-input__icon el-range__icon el-icon-date"></i>
                    <input type="text" autocomplete="off" placeholder="开始日期" class="el-range-input">
                    <span class="el-range-separator">至</span>
                    <input type="text" autocomplete="off" placeholder="结束日期" class="el-range-input">
                    <i class="el-input__icon el-range__close-icon el-icon-circle-close"></i>
                </div>
                <select class="el-select">
                    <option value="">全部线路</option>
                    <option value="1号线">1号线</option>
                    <option value="2号线">2号线</option>
                    <option value="4号线">4号线</option>
                    <option value="5号线">5号线</option>
                    <option value="6号线">6号线</option>
                </select>
                <button class="tool-btn">
                    <i>🔍</i> 查询
                </button>
            </div>
        </div>
        
        <div class="data-grid">
            <div class="data-card">
                <div class="data-title">运行总里程 (公里)</div>
                <div class="data-value">24,358</div>
                <div class="data-change positive">↑ 3.2% 较上周</div>
            </div>
            <div class="data-card">
                <div class="data-title">平均运行速度 (km/h)</div>
                <div class="data-value">42.5</div>
                <div class="data-change positive">↑ 1.5% 较上周</div>
            </div>
            <div class="data-card">
                <div class="data-title">故障发生率 (%)</div>
                <div class="data-value">0.87</div>
                <div class="data-change negative">↑ 0.2% 较上周</div>
            </div>
            <div class="data-card">
                <div class="data-title">准点率 (%)</div>
                <div class="data-value">98.6</div>
                <div class="data-change positive">↑ 0.4% 较上周</div>
            </div>
        </div>
        
        <div class="tabs">
            <div class="tab active">运行效率分析</div>
            <div class="tab">故障分析</div>
            <div class="tab">能耗分析</div>
            <div class="tab">客流分析</div>
        </div>
        
        <div class="analysis-tools">
            <button class="tool-btn">
                <i>📊</i> 趋势对比
            </button>
            <button class="tool-btn">
                <i>📈</i> 预测分析
            </button>
            <button class="tool-btn">
                <i>🔄</i> 刷新数据
            </button>
            <button class="tool-btn export-btn">
                <i>📥</i> 导出报表
            </button>
        </div>
        
        <div class="grid-2">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">各线路运行效率对比</div>
                    <select class="el-select">
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                        <option value="quarter">本季度</option>
                        <option value="year">本年度</option>
                    </select>
                </div>
                <div id="efficiency-chart" class="chart-container"></div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <div class="card-title">运行速度分布</div>
                    <select class="el-select">
                        <option value="all">全部线路</option>
                        <option value="1">1号线</option>
                        <option value="2">2号线</option>
                        <option value="4">4号线</option>
                        <option value="5">5号线</option>
                        <option value="6">6号线</option>
                    </select>
                </div>
                <div id="speed-chart" class="chart-container"></div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <div class="card-title">运行时间趋势分析</div>
                <div class="filter-group">
                    <select class="el-select">
                        <option value="day">按天</option>
                        <option value="week">按周</option>
                        <option value="month">按月</option>
                    </select>
                    <select class="el-select">
                        <option value="all">全部线路</option>
                        <option value="1">1号线</option>
                        <option value="2">2号线</option>
                        <option value="4">4号线</option>
                        <option value="5">5号线</option>
                        <option value="6">6号线</option>
                    </select>
                </div>
            </div>
            <div id="trend-chart" class="chart-container"></div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <div class="card-title">详细数据列表</div>
                <div class="filter-group">
                    <input type="text" placeholder="搜索" class="el-input">
                    <button class="tool-btn">
                        <i>🔍</i> 搜索
                    </button>
                </div>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>日期</th>
                        <th>线路</th>
                        <th>列车号</th>
                        <th>运行里程(km)</th>
                        <th>平均速度(km/h)</th>
                        <th>最高速度(km/h)</th>
                        <th>运行时间(h)</th>
                        <th>能耗(kWh)</th>
                        <th>故障次数</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>2023-07-19</td>
                        <td>1号线</td>
                        <td>T1208</td>
                        <td>320.5</td>
                        <td>42.8</td>
                        <td>80.0</td>
                        <td>7.5</td>
                        <td>1,250</td>
                        <td>0</td>
                    </tr>
                    <tr>
                        <td>2023-07-19</td>
                        <td>2号线</td>
                        <td>T0537</td>
                        <td>285.3</td>
                        <td>40.2</td>
                        <td>75.5</td>
                        <td>7.1</td>
                        <td>1,180</td>
                        <td>1</td>
                    </tr>
                    <tr>
                        <td>2023-07-19</td>
                        <td>4号线</td>
                        <td>T0912</td>
                        <td>298.7</td>
                        <td>43.5</td>
                        <td>78.2</td>
                        <td>6.9</td>
                        <td>1,220</td>
                        <td>0</td>
                    </tr>
                    <tr>
                        <td>2023-07-19</td>
                        <td>5号线</td>
                        <td>T0315</td>
                        <td>310.2</td>
                        <td>44.1</td>
                        <td>79.5</td>
                        <td>7.0</td>
                        <td>1,290</td>
                        <td>0</td>
                    </tr>
                    <tr>
                        <td>2023-07-19</td>
                        <td>6号线</td>
                        <td>T0721</td>
                        <td>275.8</td>
                        <td>39.8</td>
                        <td>72.0</td>
                        <td>6.9</td>
                        <td>1,150</td>
                        <td>2</td>
                    </tr>
                </tbody>
            </table>
            <div class="pagination">
                <button>上一页</button>
                <button class="active">1</button>
                <button>2</button>
                <button>3</button>
                <button>...</button>
                <button>10</button>
                <button>下一页</button>
            </div>
        </div>
    </div>

    <!-- 引入JS库 -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/element-ui@2.15.10/lib/index.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化日期选择器
            const datePicker = document.getElementById('date-picker');
            datePicker.addEventListener('click', function() {
                alert('日期选择器功能需要Element UI完整初始化');
            });
            
            // 初始化图表
            initCharts();
            
            // 更新时间显示
            updateTime();
            setInterval(updateTime, 1000);
            
            // 标签切换
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 在实际应用中，这里应该切换显示不同的内容
                    alert('切换到: ' + this.textContent);
                });
            });
            
            // 工具按钮点击事件
            const toolBtns = document.querySelectorAll('.tool-btn');
            toolBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    if (this.textContent.includes('导出')) {
                        alert('开始导出报表...');
                    } else if (this.textContent.includes('查询')) {
                        alert('执行查询...');
                    } else if (this.textContent.includes('趋势')) {
                        alert('显示趋势对比分析...');
                    } else if (this.textContent.includes('预测')) {
                        alert('显示预测分析...');
                    } else if (this.textContent.includes('刷新')) {
                        alert('刷新数据...');
                        initCharts(); // 重新初始化图表
                    } else if (this.textContent.includes('搜索')) {
                        alert('执行搜索...');
                    }
                });
            });
        });
        
        // 初始化图表
        function initCharts() {
            // 各线路运行效率对比图表
            const efficiencyChart = echarts.init(document.getElementById('efficiency-chart'));
            efficiencyChart.setOption({
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['准点率', '满载率', '运行效率']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['1号线', '2号线', '4号线', '5号线', '6号线']
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: [
                    {
                        name: '准点率',
                        type: 'bar',
                        data: [98.5, 97.2, 98.9, 99.1, 96.5]
                    },
                    {
                        name: '满载率',
                        type: 'bar',
                        data: [85.3, 87.2, 82.5, 80.7, 89.1]
                    },
                    {
                        name: '运行效率',
                        type: 'bar',
                        data: [92.5, 91.8, 93.2, 94.5, 90.7]
                    }
                ]
            });
            
            // 运行速度分布图表
            const speedChart = echarts.init(document.getElementById('speed-chart'));
            speedChart.setOption({
                tooltip: {
                    trigger: 'item'
                },
                legend: {
                    top: '5%',
                    left: 'center'
                },
                series: [
                    {
                        name: '速度分布',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: 15, name: '0-20km/h' },
                            { value: 25, name: '20-40km/h' },
                            { value: 40, name: '40-60km/h' },
                            { value: 15, name: '60-80km/h' },
                            { value: 5, name: '80km/h以上' }
                        ]
                    }
                ]
            });
            
            // 运行时间趋势分析图表
            const trendChart = echarts.init(document.getElementById('trend-chart'));
            trendChart.setOption({
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: ['平均速度', '运行里程', '能耗']
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
                },
                yAxis: [
                    {
                        type: 'value',
                        name: '速度/里程',
                        position: 'left'
                    },
                    {
                        type: 'value',
                        name: '能耗',
                        position: 'right'
                    }
                ],
                series: [
                    {
                        name: '平均速度',
                        type: 'line',
                        data: [42.5, 43.1, 42.8, 43.5, 44.2, 40.5, 39.8]
                    },
                    {
                        name: '运行里程',
                        type: 'line',
                        data: [3200, 3350, 3280, 3400, 3500, 2800, 2500]
                    },
                    {
                        name: '能耗',
                        type: 'line',
                        yAxisIndex: 1,
                        data: [12500, 13200, 12800, 13500, 13800, 11000, 10200]
                    }
                ]
            });
            
            // 监听窗口大小变化，重新调整图表大小
            window.addEventListener('resize', function() {
                efficiencyChart.resize();
                speedChart.resize();
                trendChart.resize();
            });
        }
        
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            
            document.querySelector('.time').textContent = 
                `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
    </script>
</body>
</html> 