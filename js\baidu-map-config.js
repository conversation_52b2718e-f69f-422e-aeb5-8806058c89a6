/**
 * 百度地图配置和工具类
 * 用于轨道交通列车运行监测系统
 */

// 百度地图配置
const BaiduMapConfig = {
    // 请替换为您的百度地图AK
    AK: 'YOUR_BAIDU_MAP_AK',
    
    // 地图默认中心点（北京市中心）
    defaultCenter: {
        lng: 116.404,
        lat: 39.915
    },
    
    // 地图默认缩放级别
    defaultZoom: 12,
    
    // 地铁线路颜色配置
    lineColors: {
        '1号线': '#C23A2A',
        '2号线': '#0066CC',
        '4号线': '#009639',
        '5号线': '#A05EB5',
        '6号线': '#CD9B1D'
    },
    
    // 地铁站点坐标（示例数据，实际项目中应从数据库获取）
    stations: {
        '1号线': [
            { name: '苹果园站', lng: 116.186, lat: 39.915 },
            { name: '古城站', lng: 116.195, lat: 39.915 },
            { name: '八角游乐园站', lng: 116.207, lat: 39.915 },
            { name: '八宝山站', lng: 116.220, lat: 39.915 },
            { name: '玉泉路站', lng: 116.240, lat: 39.915 },
            { name: '五棵松站', lng: 116.270, lat: 39.915 },
            { name: '万寿路站', lng: 116.290, lat: 39.915 },
            { name: '公主坟站', lng: 116.310, lat: 39.915 },
            { name: '军事博物馆站', lng: 116.320, lat: 39.915 },
            { name: '木樨地站', lng: 116.340, lat: 39.915 },
            { name: '南礼士路站', lng: 116.350, lat: 39.915 },
            { name: '复兴门站', lng: 116.360, lat: 39.915 },
            { name: '西单站', lng: 116.375, lat: 39.915 },
            { name: '天安门西站', lng: 116.390, lat: 39.915 },
            { name: '天安门东站', lng: 116.405, lat: 39.915 },
            { name: '王府井站', lng: 116.415, lat: 39.915 },
            { name: '东单站', lng: 116.425, lat: 39.915 },
            { name: '建国门站', lng: 116.440, lat: 39.915 },
            { name: '永安里站', lng: 116.455, lat: 39.915 },
            { name: '国贸站', lng: 116.470, lat: 39.915 },
            { name: '大望路站', lng: 116.485, lat: 39.915 },
            { name: '四惠站', lng: 116.510, lat: 39.915 },
            { name: '四惠东站', lng: 116.530, lat: 39.915 }
        ],
        '2号线': [
            { name: '西直门站', lng: 116.350, lat: 39.940 },
            { name: '车公庄站', lng: 116.360, lat: 39.940 },
            { name: '阜成门站', lng: 116.370, lat: 39.940 },
            { name: '复兴门站', lng: 116.360, lat: 39.915 },
            { name: '长椿街站', lng: 116.370, lat: 39.900 },
            { name: '宣武门站', lng: 116.380, lat: 39.890 },
            { name: '和平门站', lng: 116.390, lat: 39.885 },
            { name: '前门站', lng: 116.400, lat: 39.880 },
            { name: '崇文门站', lng: 116.420, lat: 39.890 },
            { name: '北京站', lng: 116.430, lat: 39.900 },
            { name: '建国门站', lng: 116.440, lat: 39.915 },
            { name: '朝阳门站', lng: 116.450, lat: 39.930 },
            { name: '东四十条站', lng: 116.440, lat: 39.945 },
            { name: '东直门站', lng: 116.430, lat: 39.955 },
            { name: '雍和宫站', lng: 116.420, lat: 39.955 },
            { name: '安定门站', lng: 116.410, lat: 39.955 },
            { name: '鼓楼大街站', lng: 116.395, lat: 39.955 },
            { name: '积水潭站', lng: 116.380, lat: 39.955 }
        ]
    }
};

// 百度地图工具类
class BaiduMapUtils {
    constructor() {
        this.map = null;
        this.trainMarkers = new Map(); // 存储列车标记
        this.trackPolylines = new Map(); // 存储轨迹线
        this.stationMarkers = new Map(); // 存储站点标记
    }
    
    /**
     * 初始化地图
     * @param {string} containerId 地图容器ID
     * @param {Object} options 地图选项
     */
    initMap(containerId, options = {}) {
        if (!window.BMap) {
            console.error('百度地图API未加载');
            return null;
        }
        
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`找不到地图容器: ${containerId}`);
            return null;
        }
        
        // 创建地图实例
        this.map = new BMap.Map(containerId);
        
        // 设置地图中心点和缩放级别
        const center = new BMap.Point(
            options.center?.lng || BaiduMapConfig.defaultCenter.lng,
            options.center?.lat || BaiduMapConfig.defaultCenter.lat
        );
        this.map.centerAndZoom(center, options.zoom || BaiduMapConfig.defaultZoom);
        
        // 启用地图功能
        this.map.enableScrollWheelZoom(true);
        this.map.addControl(new BMap.NavigationControl());
        this.map.addControl(new BMap.ScaleControl());
        this.map.addControl(new BMap.OverviewMapControl());
        this.map.addControl(new BMap.MapTypeControl());
        
        return this.map;
    }
    
    /**
     * 添加列车标记
     * @param {Object} train 列车信息
     * @param {Object} position 位置坐标
     */
    addTrainMarker(train, position) {
        if (!this.map) return;
        
        const point = new BMap.Point(position.lng, position.lat);
        
        // 创建自定义图标
        const icon = new BMap.Icon(
            this.createTrainIcon(train),
            new BMap.Size(32, 32),
            {
                anchor: new BMap.Size(16, 16)
            }
        );
        
        // 创建标记
        const marker = new BMap.Marker(point, { icon: icon });
        
        // 添加信息窗口
        const infoWindow = new BMap.InfoWindow(this.createTrainInfoContent(train), {
            width: 300,
            height: 200
        });
        
        marker.addEventListener('click', () => {
            this.map.openInfoWindow(infoWindow, point);
        });
        
        // 添加到地图
        this.map.addOverlay(marker);
        
        // 存储标记
        this.trainMarkers.set(train.trainId, marker);
        
        return marker;
    }
    
    /**
     * 更新列车位置
     * @param {string} trainId 列车ID
     * @param {Object} position 新位置
     */
    updateTrainPosition(trainId, position) {
        const marker = this.trainMarkers.get(trainId);
        if (marker) {
            const point = new BMap.Point(position.lng, position.lat);
            marker.setPosition(point);
        }
    }
    
    /**
     * 绘制轨迹线
     * @param {string} trainId 列车ID
     * @param {Array} trackPoints 轨迹点数组
     */
    drawTrackLine(trainId, trackPoints) {
        if (!this.map || !trackPoints.length) return;
        
        // 转换为百度地图坐标点
        const points = trackPoints.map(point => 
            new BMap.Point(point.lng, point.lat)
        );
        
        // 创建折线
        const polyline = new BMap.Polyline(points, {
            strokeColor: this.getLineColor(trainId),
            strokeWeight: 4,
            strokeOpacity: 0.8
        });
        
        // 添加到地图
        this.map.addOverlay(polyline);
        
        // 存储轨迹线
        this.trackPolylines.set(trainId, polyline);
        
        // 调整地图视野以显示完整轨迹
        this.map.setViewport(points);
        
        return polyline;
    }
    
    /**
     * 添加站点标记
     * @param {string} line 线路名称
     */
    addStationMarkers(line) {
        const stations = BaiduMapConfig.stations[line];
        if (!stations) return;
        
        stations.forEach(station => {
            const point = new BMap.Point(station.lng, station.lat);
            const marker = new BMap.Marker(point, {
                icon: new BMap.Icon(
                    this.createStationIcon(),
                    new BMap.Size(16, 16),
                    { anchor: new BMap.Size(8, 8) }
                )
            });
            
            // 添加站点标签
            const label = new BMap.Label(station.name, {
                offset: new BMap.Size(20, -10)
            });
            label.setStyle({
                color: '#333',
                fontSize: '12px',
                backgroundColor: 'rgba(255,255,255,0.8)',
                border: '1px solid #ccc',
                borderRadius: '3px',
                padding: '2px 5px'
            });
            marker.setLabel(label);
            
            this.map.addOverlay(marker);
            this.stationMarkers.set(station.name, marker);
        });
    }
    
    /**
     * 创建列车图标
     * @param {Object} train 列车信息
     */
    createTrainIcon(train) {
        const canvas = document.createElement('canvas');
        canvas.width = 32;
        canvas.height = 32;
        const ctx = canvas.getContext('2d');
        
        // 根据列车状态设置颜色
        let color = '#67C23A'; // 正常运行 - 绿色
        if (train.status === '减速运行') color = '#E6A23C'; // 黄色
        if (train.status === '临时停车') color = '#F56C6C'; // 红色
        
        // 绘制列车图标
        ctx.fillStyle = color;
        ctx.fillRect(4, 8, 24, 16);
        ctx.fillStyle = '#fff';
        ctx.font = '10px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(train.trainId.slice(-4), 16, 18);
        
        return canvas.toDataURL();
    }
    
    /**
     * 创建站点图标
     */
    createStationIcon() {
        const canvas = document.createElement('canvas');
        canvas.width = 16;
        canvas.height = 16;
        const ctx = canvas.getContext('2d');
        
        ctx.fillStyle = '#409EFF';
        ctx.beginPath();
        ctx.arc(8, 8, 6, 0, 2 * Math.PI);
        ctx.fill();
        
        ctx.fillStyle = '#fff';
        ctx.beginPath();
        ctx.arc(8, 8, 3, 0, 2 * Math.PI);
        ctx.fill();
        
        return canvas.toDataURL();
    }
    
    /**
     * 创建列车信息窗口内容
     * @param {Object} train 列车信息
     */
    createTrainInfoContent(train) {
        return `
            <div style="padding: 10px;">
                <h4 style="margin: 0 0 10px 0; color: #409EFF;">列车 ${train.trainId}</h4>
                <p><strong>线路：</strong>${train.line}</p>
                <p><strong>状态：</strong><span style="color: ${this.getStatusColor(train.status)}">${train.status}</span></p>
                <p><strong>速度：</strong>${train.speed}</p>
                <p><strong>位置：</strong>${train.position}</p>
                <p><strong>下一站：</strong>${train.nextStation}</p>
                <p><strong>预计到达：</strong>${train.arrivalTime}</p>
                <p><strong>温度：</strong>${train.temperature}</p>
                <p><strong>乘客：</strong>${train.passengers}</p>
            </div>
        `;
    }
    
    /**
     * 获取线路颜色
     * @param {string} trainId 列车ID
     */
    getLineColor(trainId) {
        // 根据列车ID推断线路（简化逻辑）
        if (trainId.includes('T12')) return BaiduMapConfig.lineColors['1号线'];
        if (trainId.includes('T05')) return BaiduMapConfig.lineColors['2号线'];
        if (trainId.includes('T09')) return BaiduMapConfig.lineColors['4号线'];
        if (trainId.includes('T03')) return BaiduMapConfig.lineColors['5号线'];
        if (trainId.includes('T07')) return BaiduMapConfig.lineColors['6号线'];
        return '#409EFF';
    }
    
    /**
     * 获取状态颜色
     * @param {string} status 状态
     */
    getStatusColor(status) {
        const colorMap = {
            '正常运行': '#67C23A',
            '减速运行': '#E6A23C',
            '临时停车': '#F56C6C'
        };
        return colorMap[status] || '#909399';
    }
    
    /**
     * 清除所有标记
     */
    clearAllMarkers() {
        if (!this.map) return;
        
        this.trainMarkers.forEach(marker => {
            this.map.removeOverlay(marker);
        });
        this.trainMarkers.clear();
        
        this.trackPolylines.forEach(polyline => {
            this.map.removeOverlay(polyline);
        });
        this.trackPolylines.clear();
        
        this.stationMarkers.forEach(marker => {
            this.map.removeOverlay(marker);
        });
        this.stationMarkers.clear();
    }
    
    /**
     * 销毁地图
     */
    destroy() {
        if (this.map) {
            this.clearAllMarkers();
            this.map = null;
        }
    }
}

// 导出配置和工具类
window.BaiduMapConfig = BaiduMapConfig;
window.BaiduMapUtils = BaiduMapUtils;
